suite: test secret
templates:
  - secret.yaml
tests:
  - it: should not render secret with default provider when no api key
    asserts:
      - hasDocuments:
          count: 0

  - it: should contain api key when provided for openai
    set:
      providers:
        openAI:
          apiKey: "sk-test-key"
    asserts:
      - isKind:
          of: Secret
      - equal:
          path: metadata.name
          value: kagent-openai
      - equal:
          path: type
          value: Opaque
      - equal:
          path: data.OPENAI_API_KEY
          value: c2stdGVzdC1rZXk=  # base64 of "sk-test-key"
      - hasDocuments:
          count: 1

  - it: should render anthropic secret when anthropic provider is default
    set:
      providers:
        default: anthropic
        anthropic:
          apiKey: "anthropic-test-key"
    asserts:
      - equal:
          path: metadata.name
          value: kagent-anthropic
      - equal:
          path: data.ANTHROPIC_API_KEY
          value: YW50aHJvcGljLXRlc3Qta2V5  # base64 of "anthropic-test-key"

  - it: should render azure openai secret when azure provider is default
    set:
      providers:
        default: azureOpenAI
        azureOpenAI:
          apiKey: "azure-test-key"
    asserts:
      - equal:
          path: metadata.name
          value: kagent-azure-openai
      - equal:
          path: data.AZUREOPENAI_API_KEY
          value: YXp1cmUtdGVzdC1rZXk=  # base64 of "azure-test-key"

  - it: should not render secret when no api key provided
    set:
      providers:
        openAI:
          apiKey: ""
    asserts:
      - hasDocuments:
          count: 0

  - it: should have correct labels
    set:
      providers:
        openAI:
          apiKey: "test-key"
    asserts:
      - equal:
          path: metadata.labels["app.kubernetes.io/name"]
          value: kagent
      - equal:
          path: metadata.labels["app.kubernetes.io/instance"]
          value: RELEASE-NAME
      - equal:
          path: metadata.labels["app.kubernetes.io/managed-by"]
          value: Helm

  - it: should be in correct namespace
    set:
      providers:
        openAI:
          apiKey: "test-key"
    asserts:
      - equal:
          path: metadata.namespace
          value: NAMESPACE

  - it: should use custom namespace when overridden
    set:
      namespaceOverride: "custom-namespace"
      providers:
        openAI:
          apiKey: "test-key"
    asserts:
      - equal:
          path: metadata.namespace
          value: custom-namespace 