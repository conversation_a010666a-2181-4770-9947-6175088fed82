apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: missing-service-selector
spec:
  description: Service selector is missing, service does not route to pods
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v2' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch service backend-v2 --context ${CLUSTER_CTX} -p '{"spec":{"selector": null}}'