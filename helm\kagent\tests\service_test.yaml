suite: test service
templates:
  - service.yaml
tests:
  - it: should render service with default values
    asserts:
      - isKind:
          of: Service
      - equal:
          path: metadata.name
          value: RELEASE-NAME
      - equal:
          path: spec.type
          value: ClusterIP
      - hasDocuments:
          count: 1

  - it: should have correct port configuration
    asserts:
      - equal:
          path: spec.ports[0].name
          value: ui
      - equal:
          path: spec.ports[0].port
          value: 80
      - equal:
          path: spec.ports[0].targetPort
          value: 8080
      - equal:
          path: spec.ports[0].protocol
          value: TCP
      - equal:
          path: spec.ports[1].name
          value: app
      - equal:
          path: spec.ports[1].port
          value: 8081
      - equal:
          path: spec.ports[1].targetPort
          value: 8081
      - equal:
          path: spec.ports[2].name
          value: controller
      - equal:
          path: spec.ports[2].port
          value: 8083
      - equal:
          path: spec.ports[2].targetPort
          value: 8083

  - it: should have correct selector labels
    asserts:
      - equal:
          path: spec.selector["app.kubernetes.io/name"]
          value: kagent
      - equal:
          path: spec.selector["app.kubernetes.io/instance"]
          value: RELEASE-NAME

  - it: should use custom service type when set
    set:
      service:
        type: <PERSON><PERSON><PERSON><PERSON><PERSON>
    asserts:
      - equal:
          path: spec.type
          value: LoadBalancer

  - it: should use custom ports when configured
    set:
      service:
        ports:
          ui:
            port: 8080
            targetPort: 8080
          app:
            port: 9000
            targetPort: 9000
          controller:
            port: 9001
            targetPort: 9001
    asserts:
      - equal:
          path: spec.ports[0].port
          value: 8080
      - equal:
          path: spec.ports[0].targetPort
          value: 8080
      - equal:
          path: spec.ports[1].port
          value: 9000
      - equal:
          path: spec.ports[1].targetPort
          value: 9000
      - equal:
          path: spec.ports[2].port
          value: 9001
      - equal:
          path: spec.ports[2].targetPort
          value: 9001

  - it: should have correct metadata labels
    asserts:
      - equal:
          path: metadata.labels["app.kubernetes.io/name"]
          value: kagent
      - equal:
          path: metadata.labels["app.kubernetes.io/instance"]
          value: RELEASE-NAME
      - equal:
          path: metadata.labels["app.kubernetes.io/managed-by"]
          value: Helm
      - isNotEmpty:
          path: metadata.labels["helm.sh/chart"]

  - it: should be in correct namespace
    asserts:
      - equal:
          path: metadata.namespace
          value: NAMESPACE

  - it: should use custom namespace when overridden
    set:
      namespaceOverride: "custom-namespace"
    asserts:
      - equal:
          path: metadata.namespace
          value: custom-namespace 