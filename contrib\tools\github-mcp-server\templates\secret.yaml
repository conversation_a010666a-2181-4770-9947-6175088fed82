{{- if .Values.tokenSecret.value }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.tokenSecret.name | default (printf "%s-token" .Release.Name) }}
  labels:
    {{- include "github-mcp-server.labels" . | nindent 4 }}
type: Opaque
stringData:
  {{ .Values.tokenSecret.key | default "token" }}: {{ printf "Bearer %s" .Values.tokenSecret.value | quote }}
{{- end }}
{{- range $toolset, $config := .Values.tools }}
{{- if and $config.tokenSecret $config.tokenSecret.value }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "github-mcp-server.tokenSecretName" (dict "toolset" $toolset "config" $config "context" $) }}
  labels:
    {{- include "github-mcp-server.labels" $ | nindent 4 }}
type: Opaque
stringData:
  {{ $config.tokenSecret.key | default "token" }}: {{ printf "Bearer %s" $config.tokenSecret.value | quote }}
{{- end }}
{{- end }}
