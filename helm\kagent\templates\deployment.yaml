apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "kagent.fullname" . }}
  namespace: {{ include "kagent.namespace" . }}
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "kagent.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "kagent.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      serviceAccountName: {{ include "kagent.fullname" . }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
      - name: sqlite-volume
        emptyDir:
          sizeLimit: 500Mi
          medium: Memory
      containers:
        - name: controller
          args:
          {{/* #we need to pass the default model config name to the app otherwise helm upgrade will not allow provider type change due to validations */}}
            - -default-model-config-name
            - {{ include "kagent.defaultModelConfigName" . | quote }}
            - -zap-log-level
            - {{ .Values.controller.loglevel }}
            - -watch-namespaces
            - "{{ include "kagent.watchNamespaces" . }}"
            - -database-type
            - {{ .Values.database.type }}
            {{- if eq .Values.database.type "sqlite" }}
            - -sqlite-database-path
            - /sqlite-volume/{{ .Values.database.sqlite.databaseName }}
            {{- else if eq .Values.database.type "postgres" }}
            - -postgres-database-url
            - {{ .Values.database.postgres.url }}
            {{- end }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.controller.image.registry }}/{{ .Values.controller.image.repository }}:{{ coalesce .Values.global.tag .Values.controller.image.tag .Chart.Version }}"
          imagePullPolicy: {{ .Values.controller.image.pullPolicy }}
          resources:
            {{- toYaml .Values.controller.resources | nindent 12 }}
          env:
            - name: KAGENT_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          {{- with .Values.controller.env }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http-controller
              containerPort: {{ .Values.service.ports.controller.targetPort }}
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: http-controller
            initialDelaySeconds: 15
            periodSeconds: 15
          volumeMounts:
            - name: sqlite-volume
              mountPath: /sqlite-volume
        - name: app
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.app.image.registry }}/{{ .Values.app.image.repository }}:{{ coalesce .Values.global.tag .Values.app.image.tag .Chart.Version }}"
          imagePullPolicy: {{ .Values.app.image.pullPolicy }}
          env:
            - name: LOG_LEVEL
              value: {{ .Values.app.loglevel | quote }}
            - name: OTEL_TRACING_ENABLED
              value: {{ .Values.otel.tracing.enabled | quote }}
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .Values.otel.tracing.exporter.otlp.endpoint | quote }}
            - name: OTEL_EXPORTER_OTLP_TRACES_TIMEOUT
              value: {{ .Values.otel.tracing.exporter.otlp.timeout | quote }}
            - name: OTEL_EXPORTER_OTLP_TRACES_INSECURE
              value: {{ .Values.otel.tracing.exporter.otlp.insecure | quote }}
            - name: AUTOGEN_DISABLE_RUNTIME_TRACING
              value: "true"
          {{- with .Values.app.env }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http-app
              containerPort: {{ .Values.service.ports.app.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.app.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /api/version
              port: http-app
            initialDelaySeconds: 15
            periodSeconds: 15
        - name: ui
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.ui.image.registry }}/{{ .Values.ui.image.repository }}:{{ coalesce .Values.global.tag .Values.ui.image.tag .Chart.Version }}"
          imagePullPolicy: {{ .Values.ui.image.pullPolicy }}
          env:
            - name: NEXT_PUBLIC_BACKEND_URL
              value: "http://{{ include "kagent.fullname" . }}.{{ include "kagent.namespace" . }}.svc.cluster.local/api"
          {{- with .Values.ui.env }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http-ui
              containerPort: {{ .Values.service.ports.ui.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.ui.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /
              port: http-ui
            initialDelaySeconds: 15
            periodSeconds: 15
        - name: tools
          command:
            - /tool-server
          args:
          - "--port"
          - "{{ .Values.service.ports.tools.targetPort }}"
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.tools.image.registry }}/{{ .Values.tools.image.repository }}:{{ coalesce .Values.global.tag .Values.tools.image.tag .Chart.Version }}"
          imagePullPolicy: {{ .Values.tools.image.pullPolicy }}
          resources:
            {{- toYaml .Values.tools.resources | nindent 12 }}
          env:
            - name: KAGENT_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "kagent.fullname" . }}-openai
                  key: OPENAI_API_KEY
                  optional: true # if the secret is not found, the tool will not be available
            - name: OTEL_TRACING_ENABLED
              value: {{ .Values.otel.tracing.enabled | quote }}
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .Values.otel.tracing.exporter.otlp.endpoint | quote }}
            - name: OTEL_EXPORTER_OTLP_TRACES_TIMEOUT
              value: {{ .Values.otel.tracing.exporter.otlp.timeout | quote }}
            - name: OTEL_EXPORTER_OTLP_TRACES_INSECURE
              value: {{ .Values.otel.tracing.exporter.otlp.insecure | quote }}
          {{- with .Values.tools.env }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http-tools
              containerPort: {{ .Values.service.ports.tools.targetPort }}
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: http-tools
            initialDelaySeconds: 15
            periodSeconds: 15
        - name: querydoc
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.mcp.doc2vec.image.registry }}/{{ .Values.mcp.doc2vec.image.repository }}:{{ .Values.mcp.doc2vec.image.tag }}"
          imagePullPolicy: {{ .Values.mcp.doc2vec.image.pullPolicy }}
          resources:
            {{- toYaml .Values.mcp.doc2vec.resources | nindent 12 }}
          env:
            - name: KAGENT_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: KAGENT_LOG_LEVEL
              value: {{ .Values.tools.loglevel }}
            - name: PORT
              value: "{{ .Values.service.ports.querydoc.targetPort }}"
            - name: TRANSPORT_TYPE
              value: "http"
            - name: STRICT_MODE
              value: "false"
            {{- if and .Values.mcp.doc2vec.enabled (eq .Values.providers.default "openAI") }}
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "kagent.fullname" . }}-openai
                  key: OPENAI_API_KEY
            {{- end }}
            - name: OTEL_TRACING_ENABLED
              value: {{ .Values.otel.tracing.enabled | quote }}
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .Values.otel.tracing.exporter.otlp.endpoint | quote }}
            - name: OTEL_EXPORTER_OTLP_TRACES_TIMEOUT
              value: {{ .Values.otel.tracing.exporter.otlp.timeout | quote }}
            - name: OTEL_EXPORTER_OTLP_TRACES_INSECURE
              value: {{ .Values.otel.tracing.exporter.otlp.insecure | quote }}
          {{- with .Values.mcp.doc2vec.env }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
          ports:
            - name: http-querydoc
              containerPort: {{ .Values.service.ports.querydoc.targetPort }}
              protocol: TCP
