## Security vulnerabilities

Review how the kagent project handles the lifecycle of Common Vulnerability and Exposures (CVEs).

### 📨 Where to report

To report a security vulnerability, email the private <NAME_EMAIL>.

### ✅ When to send a report

Send a report when:

    You discover that a kagent component has a potential security vulnerability.
    You are unsure whether or how a vulnerability affects kagent.

### 🔔 Check before sending

If in doubt, send a private message about potential vulnerabilities such as:

    Any crash, especially in kagent.
    Any potential Denial of Service (DoS) attack.

### ❌ When NOT to send a report

Do not send a report for vulnerabilities that are not part of the kagent project, such as:

    You want help configuring kagent components for security purposes.
    You want help applying security related updates to your kagent configuration or environment.
    Your issue is not related to security vulnerabilities.
    Your issue is related to base image dependencies, such as AutoGen.

### Evaluation

The kagent team evaluates vulnerability reports for:

    Severity level, which can affect the priority of the fix
    Impact of the vulnerability on kagent code as opposed to backend code
    Potential dependencies on third-party or backend code that might delay the remediation process

The kagent team strives to keep private any vulnerability information with us as part of the remediation process. We only share information on a need-to-know basis to address the issue.
