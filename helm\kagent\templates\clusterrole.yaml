apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kagent.fullname" . }}-getter-role
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
rules:
- apiGroups:
  - kagent.dev
  resources:
  - agents
  - modelconfigs
  - teams
  - toolservers
  - memories
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - kagent.dev
  resources:
  - agents/status
  - modelconfigs/status
  - teams/status
  - toolservers/status
  - memories/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - "*"
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - "apps"
  resources:
  - "*"
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - "batch"
  resources:
  - "*"
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - "rbac.authorization.k8s.io"
  resources:
  - "*"
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - "apiextensions.k8s.io"
  resources:
  - "*"
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - gateway.networking.k8s.io/v1
  resources:
  - "*"
  verbs:
  - get
  - list
  - watch
---

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kagent.fullname" . }}-writer-role
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
rules:
- apiGroups:
  - kagent.dev
  resources:
  - agents
  - modelconfigs
  - teams
  - toolservers
  - memories
  verbs:
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - "*"
  verbs:
  - create
  - update
  - patch
  - delete
- apiGroups:
  - "apps"
  resources:
  - "*"
  verbs:
  - create
  - update
  - patch
  - delete
- apiGroups:
  - "batch"
  resources:
  - "*"
  verbs:
  - create
  - update
  - patch
  - delete
- apiGroups:
  - "apiextensions.k8s.io"
  resources:
  - "*"
  verbs:
  - create
  - update
  - patch
  - delete
- apiGroups:
  - gateway.networking.k8s.io/v1
  resources:
  - "*"
  verbs:
  - create
  - update
  - patch
  - delete
