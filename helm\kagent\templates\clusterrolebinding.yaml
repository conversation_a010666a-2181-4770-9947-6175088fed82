apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kagent.fullname" . }}-getter-rolebinding
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kagent.fullname" . }}-getter-role
subjects:
- kind: ServiceAccount
  name: {{ include "kagent.fullname" . }}
  namespace: {{ include "kagent.namespace" . }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kagent.fullname" . }}-writer-rolebinding
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kagent.fullname" . }}-writer-role
subjects:
- kind: ServiceAccount
  name: {{ include "kagent.fullname" . }}
  namespace: {{ include "kagent.namespace" . }}