# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: kagent.dev
layout:
- go.kubebuilder.io/v4
projectName: controller
repo: github.com/kagent-dev/kagent/go/controller
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: kagent.dev
  group: agent
  kind: AutogenTeam
  path: github.com/kagent-dev/kagent/go/controller/api/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: kagent.dev
  group: agent
  kind: AutogenAgent
  path: github.com/kagent-dev/kagent/go/controller/api/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: kagent.dev
  group: agent
  kind: AutogenModelConfig
  path: github.com/kagent-dev/kagent/go/controller/api/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: kagent.dev
  group: agent
  kind: ToolServer
  path: github.com/kagent-dev/kagent/go/controller/api/v1alpha1
  version: v1alpha1
version: "3"
