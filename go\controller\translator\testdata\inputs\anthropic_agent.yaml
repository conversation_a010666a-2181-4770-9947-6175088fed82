operation: translateAgent
targetObject: anthropic-agent
namespace: test
objects:
  - apiVersion: v1
    kind: Secret
    metadata:
      name: anthropic-secret
      namespace: test
    data:
      api-key: YW50aHJvcGljLWFwaS1rZXk=  # base64 encoded "anthropic-api-key"
  - apiVersion: kagent.dev/v1alpha1
    kind: ModelConfig
    metadata:
      name: anthropic-model
      namespace: test
    spec:
      provider: Anthropic
      model: claude-3-sonnet-20240229
      apiKeySecretRef: anthropic-secret
      apiKeySecretKey: api-key
      anthropic:
        baseURL: "https://api.anthropic.com"
        temperature: "0.3"
        maxTokens: 4096
        topP: "0.9"
        topK: 40
  - apiVersion: kagent.dev/v1alpha1
    kind: Agent
    metadata:
      name: anthropic-agent
      namespace: test
    spec:
      description: An agent using Anthropic Claude
      systemMessage: You are <PERSON>, an AI assistant created by Anthropic.
      modelConfig: anthropic-model
      tools: [] 