"use client";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export default function KAgentLogoWithText({ className } : { className?: string }) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  let fill;
  if (resolvedTheme === "dark") {
    fill = "white";
  } else {
    fill = "#151927";
  }

  return (
    <svg viewBox="0 0 494 110" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <g clipPath="url(#clip0_16068_64093)">
        <path d="M91.609 46.098H76.3328V61.472H91.609V46.098Z" fill="#942DE7" />
        <path d="M61.0806 46.098H45.8045V61.472H61.0806V46.098Z" fill="#942DE7" />
        <path
          d="M106.861 15.374L91.5851 0H76.3329H61.0567H45.8045H30.5284V15.374H15.2761V30.724H0V46.098V61.4479L15.2761 76.822L30.5284 92.1719H45.8045H61.0567H76.3329H91.5851H106.861V76.822H91.5851H76.3329H61.0567H45.8045H30.5284V61.4479V46.098V30.724H45.8045H61.0567H76.3329H91.5851H106.861V46.098V61.4479V76.822H122.113V61.4479V46.098V30.724L106.861 15.374Z"
          fill="#942DE7"
        />
        <path
          d="M204.805 83.0293C205.164 83.6549 204.734 84.4488 204.016 84.4488H185.226C184.891 84.4488 184.581 84.2563 184.413 83.9676L175.138 66.5245C174.683 65.6824 173.392 65.9952 173.392 66.9576V83.5346C173.392 84.0398 172.986 84.4729 172.46 84.4729H156.276C155.774 84.4729 155.343 84.0639 155.343 83.5346V8.68548C155.343 8.18023 155.75 7.74716 156.276 7.74716H172.46C172.962 7.74716 173.392 8.15617 173.392 8.68548V43.6199C173.392 44.5822 174.683 44.9191 175.138 44.0529L184.413 26.6098C184.581 26.297 184.891 26.1286 185.226 26.1286H204.016C204.734 26.1286 205.188 26.9226 204.805 27.5481L188.549 54.8075C188.382 55.0962 188.382 55.4812 188.549 55.7699L204.805 83.0293Z"
          fill={fill}
        />
        <path
          d="M265.36 27.0188V83.4624C265.36 83.9676 264.954 84.4007 264.428 84.4007H248.243C247.741 84.4007 247.311 83.9917 247.311 83.4624V82.5962C247.311 82.2354 246.928 82.0188 246.617 82.1872C241.98 84.8338 236.72 86.2052 231.341 86.2052C214.368 86.2052 200.574 72.5153 200.574 55.6737C200.574 38.832 214.368 25.1422 231.341 25.1422C236.72 25.1422 241.956 26.5376 246.617 29.1601C246.928 29.3285 247.311 29.112 247.311 28.7511V26.9707C247.311 26.4655 247.717 26.0324 248.243 26.0324H264.428C264.93 26.0324 265.36 26.4414 265.36 26.9707V27.0188ZM247.311 55.6977C247.311 47.9746 241.908 41.6951 234.114 41.6951C226.321 41.6951 220.01 47.9746 220.01 55.6977C220.01 63.4208 226.345 69.7004 234.114 69.7004C241.884 69.7004 247.311 63.4208 247.311 55.6977Z"
          fill={fill}
        />
        <path
          d="M333.852 27.1872V78.867C333.852 98.9326 323.118 109.976 303.61 109.976H303.108C302.893 109.976 302.654 109.976 302.391 109.976C298.016 109.976 287.163 109.423 274.731 98.0424C274.301 97.6575 274.325 96.9598 274.779 96.5989L286.684 87.36C287.067 87.0713 287.593 87.1194 287.904 87.4563C290.486 90.1269 296.223 94.9147 303.634 94.4335C308.487 94.1208 312.432 92.2682 314.44 89.381C315.802 87.4322 316.233 85.0984 315.755 82.5241C315.683 82.2113 315.348 82.0429 315.061 82.2113C310.447 84.8097 305.236 86.2052 299.905 86.2052C283.003 86.2052 269.257 72.5634 269.257 55.7699C269.257 38.9764 283.003 25.3347 299.905 25.3347C305.26 25.3347 310.495 26.706 315.109 29.3526C315.42 29.521 315.802 29.3045 315.802 28.9436V27.1872C315.802 26.682 316.209 26.2489 316.735 26.2489H332.871C333.374 26.2489 333.804 26.6579 333.804 27.1872H333.852ZM315.874 55.7699C315.874 54.1098 315.587 52.4738 314.99 50.934C312.958 45.4965 308.583 41.8154 302.726 41.8154C294.98 41.8154 288.669 48.0709 288.669 55.7699C288.669 63.469 294.645 69.4838 302.295 69.7244H302.415C302.415 69.7244 302.63 69.7244 302.726 69.7244C310.471 69.7244 315.874 63.469 315.874 55.7699Z"
          fill={fill}
        />
        <path
          d="M399.355 55.7218C399.355 57.4059 399.164 59.3788 398.853 61.1352C398.781 61.5923 398.399 61.9051 397.944 61.9051H357.352C356.634 61.9051 356.18 62.699 356.563 63.3246C359.049 67.5831 363.519 70.2297 368.444 70.2297C372.628 70.2297 376.548 68.3771 379.321 64.7922C379.537 64.5276 379.871 64.3832 380.23 64.4313L395.769 67.2463C396.367 67.3666 396.701 68.0162 396.438 68.5696C396.08 69.2913 395.649 70.2056 395.386 70.6627L395.124 71.0958C394.884 71.5048 394.645 71.8898 394.359 72.3469C394 72.9003 393.665 73.3815 393.331 73.8386L393.139 74.1032C393.02 74.2476 392.924 74.3919 392.805 74.5363L392.637 74.7528C392.422 75.0175 392.207 75.2822 392.016 75.5227L391.824 75.7393C391.49 76.1242 391.131 76.5092 390.773 76.8941L390.557 77.1107C390.318 77.3513 390.079 77.5919 389.816 77.8325L389.745 77.9046C389.482 78.1693 389.195 78.4099 388.908 78.6745L388.597 78.9392L388.238 78.6264L388.549 78.9873C388.119 79.3482 387.713 79.685 387.306 79.9978L386.948 80.2625C386.469 80.6234 385.991 80.9602 385.489 81.273L385.346 81.3692C384.772 81.7301 384.198 82.091 383.625 82.4038L383.457 82.5C383.314 82.5722 383.17 82.6444 383.027 82.7165L382.931 82.7647C382.668 82.909 382.429 83.0293 382.19 83.1496L382.047 83.2218C381.497 83.4864 380.971 83.727 380.469 83.9436L380.373 83.9917C380.015 84.136 379.728 84.2563 379.417 84.3766L379.035 84.521C378.652 84.6654 378.27 84.7857 377.887 84.9059H377.815C377.409 85.0503 376.979 85.1706 376.477 85.315L376.118 85.4112C375.233 85.6277 374.349 85.8202 373.464 85.9405L373.13 85.9886C372.197 86.133 371.265 86.2292 370.357 86.2773H370.022C369.472 86.3255 369.018 86.3255 368.611 86.3255C368.205 86.3255 367.727 86.3255 367.177 86.2773C367.081 86.2773 366.962 86.2773 366.866 86.2773H366.771C366.412 86.2533 366.077 86.2292 365.719 86.1811H365.599C365.599 86.1811 365.408 86.157 365.288 86.133C364.786 86.0849 364.332 86.0367 363.95 85.9646L363.711 85.9164C363.328 85.8683 362.97 85.7962 362.587 85.724L362.181 85.6277C361.846 85.5556 361.511 85.4834 361.177 85.4112H361.081C361.081 85.4112 360.89 85.339 360.794 85.315C360.292 85.1947 359.886 85.0744 359.479 84.9541C359.049 84.8097 358.619 84.6654 358.188 84.521C358.093 84.4729 357.997 84.4488 357.877 84.4007H357.806C357.495 84.2563 357.184 84.136 356.873 84.0157H356.778C356.778 84.0157 356.587 83.8955 356.491 83.8714C356.085 83.703 355.678 83.5105 355.272 83.318H355.224C354.865 83.1255 354.459 82.909 354.076 82.7165C353.981 82.6684 353.885 82.6203 353.79 82.5722L353.718 82.5241C353.431 82.3556 353.144 82.2113 352.857 82.0429L352.522 81.8504C352.14 81.6098 351.781 81.3933 351.399 81.1286C347.741 78.6745 344.586 75.3784 342.291 71.6251C342.099 71.3123 341.908 70.9996 341.741 70.6627L341.669 70.5184C341.502 70.2056 341.334 69.8928 341.167 69.5801L341.071 69.4116C341.071 69.4116 340.976 69.2192 340.928 69.1229C340.354 67.944 339.828 66.6689 339.398 65.3456C339.159 64.6238 338.968 63.9983 338.824 63.4208C338.705 62.9396 338.585 62.4344 338.489 61.9532C338.179 60.4615 337.987 58.9217 337.916 57.3819C337.892 56.8526 337.868 56.3233 337.868 55.818C337.868 55.3128 337.868 54.7594 337.916 54.2542C338.059 51.5595 338.561 48.8648 339.422 46.2664C339.709 45.4243 339.996 44.6544 340.282 43.9326C340.45 43.5236 340.641 43.1146 340.832 42.7297C340.88 42.6334 340.928 42.5372 340.976 42.4409L341.095 42.2004C341.095 42.2004 341.191 42.0079 341.239 41.9116C341.287 41.8154 341.334 41.7192 341.382 41.6229L341.645 41.1177C341.86 40.7327 342.075 40.3478 342.314 39.9628C344.61 36.2095 347.765 32.9374 351.423 30.4593C351.781 30.2187 352.164 29.9781 352.546 29.7375L352.881 29.5451C353.168 29.3766 353.455 29.2082 353.742 29.0639L353.813 29.0158C353.813 29.0158 354.005 28.9195 354.1 28.8714C354.483 28.6549 354.889 28.4624 355.296 28.2699C355.702 28.0774 356.108 27.885 356.515 27.7165C356.61 27.6684 356.706 27.6444 356.802 27.5962H356.897C357.208 27.4278 357.519 27.3075 357.83 27.1872H357.925C357.925 27.1872 358.117 27.091 358.212 27.0429C358.642 26.8985 359.073 26.7542 359.503 26.6098C359.933 26.4654 360.388 26.3451 360.818 26.2489C360.914 26.2248 361.009 26.2008 361.105 26.1767H361.2C361.535 26.0805 361.87 25.9843 362.205 25.9361L362.444 25.888C362.85 25.7918 363.495 25.6955 363.902 25.6234H363.974C364.38 25.5512 364.81 25.5031 365.336 25.4549H365.647L365.743 25.4068C366.077 25.3828 366.436 25.3587 366.795 25.3346H366.866C366.866 25.3346 367.081 25.3346 367.201 25.3106C367.727 25.3106 368.205 25.2865 368.635 25.2865C369.066 25.2865 369.496 25.2865 370.046 25.3106H370.38C371.313 25.3828 372.245 25.479 373.13 25.5993L373.488 25.6474C374.373 25.7918 375.257 25.9602 376.118 26.1767L376.501 26.273C377.003 26.3933 377.433 26.5136 377.815 26.6579H377.887C378.294 26.8023 378.676 26.9466 379.035 27.0669C379.154 27.1151 379.274 27.1391 379.393 27.1872C379.752 27.3075 380.039 27.4278 380.349 27.5722L380.493 27.6444C380.995 27.8609 381.521 28.1015 382.047 28.3421L382.19 28.4143C382.453 28.5346 382.692 28.6549 382.931 28.7992L383.003 28.8473L382.812 29.2563L383.051 28.8714C383.194 28.9436 383.338 29.0158 383.481 29.112C384.007 29.4007 384.844 29.906 385.346 30.2428L385.418 30.2909C385.944 30.6277 386.469 30.9886 386.948 31.3495L387.019 31.4217C387.019 31.4217 387.211 31.5661 387.306 31.6382C387.736 31.951 388.143 32.2878 388.573 32.6487L388.812 32.8653C389.147 33.1299 389.458 33.4186 389.768 33.7074L389.84 33.7795C390.079 34.0201 390.342 34.2607 390.581 34.5013L390.773 34.6938C391.155 35.1028 391.514 35.4637 391.824 35.8486L391.514 36.2095L391.872 35.8968L392.04 36.0892C392.255 36.3298 392.446 36.5704 392.661 36.8351L392.852 37.0757C393.091 37.3644 393.331 37.6772 393.546 37.9899V38.0381C393.761 38.2787 393.952 38.5433 394.119 38.7839L394.454 39.2892C394.598 39.5057 394.741 39.7463 394.884 39.9628L395.458 40.9011C396.606 42.9221 397.514 45.0634 398.16 47.2528C398.996 50.0678 399.403 52.9549 399.403 55.8661L399.355 55.7218ZM381.21 47.9746C380.493 43.668 375.544 40.3237 369.257 40.3237C363.543 40.3237 358.117 43.6439 357.328 47.9506C357.232 48.528 357.662 49.0573 358.236 49.0573H380.278C380.851 49.0573 381.282 48.528 381.186 47.9746H381.21Z"
          fill={fill}
        />
        <path
          d="M456.108 50.6693V83.4864C456.108 83.9917 455.702 84.4247 455.176 84.4247H437.892C437.39 84.4247 436.96 84.0157 436.96 83.4864V51.5835C436.96 49.4182 436.84 46.9401 435.932 44.9431C435.525 44.101 434.975 43.3793 434.306 42.874C433.23 42.0319 431.82 41.5989 430.027 41.5989C426.68 41.5989 424.098 42.7537 423.285 44.6063C422.353 46.6273 422.353 49.3701 422.353 51.5835V83.4864C422.353 83.9917 421.946 84.4247 421.42 84.4247H404.136C403.634 84.4247 403.204 84.0157 403.204 83.4864V27.0429C403.204 26.5376 403.61 26.1045 404.136 26.1045H420.321C420.823 26.1045 421.253 26.5136 421.253 27.0429V28.6067C421.253 28.9676 421.66 29.2082 421.97 28.9917C435.023 19.9453 447.717 29.9781 447.861 30.0984C448.315 30.4593 448.769 30.8443 449.176 31.2533C455.081 37.2441 456.085 42.08 456.085 50.6693H456.108Z"
          fill={fill}
        />
        <path
          d="M494 70.35L491.92 84.1361C491.848 84.5691 491.49 84.906 491.059 84.93C489.075 85.0503 483.768 85.339 482.142 85.339C482.023 85.339 481.903 85.339 481.832 85.339C471.982 84.8097 466.795 79.7332 466.795 70.6387V42.6334C466.795 42.1282 466.388 41.6951 465.862 41.6951H459.838C459.336 41.6951 458.906 41.2861 458.906 40.7568V26.9707C458.906 26.4655 459.312 26.0324 459.838 26.0324H465.862C466.364 26.0324 466.795 25.6234 466.795 25.0941V9.11856C466.795 8.61331 467.201 8.18024 467.727 8.18024H483.911C484.414 8.18024 484.844 8.58925 484.844 9.11856V25.1181C484.844 25.6234 485.25 26.0564 485.776 26.0564H491.801C492.303 26.0564 492.733 26.4655 492.733 26.9948V40.7808C492.733 41.2861 492.327 41.7192 491.801 41.7192H485.776C485.274 41.7192 484.844 42.1282 484.844 42.6575C484.844 47.4934 484.844 61.8088 484.844 61.9532V62.3622C484.772 64.1426 484.7 66.5726 486.111 68.0162C486.948 68.8583 488.239 69.2913 489.936 69.2913H493.092C493.665 69.2913 494.096 69.7966 494 70.35Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_16068_64093">
          <rect width="494" height="110" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
