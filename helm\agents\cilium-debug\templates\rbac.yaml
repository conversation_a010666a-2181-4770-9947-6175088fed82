apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kagent.fullname" . }}-cilium-debug-role
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
rules:
# Access to Cilium CRDs for debugging
- apiGroups:
    - 'cilium.io'
  resources:
    - '*'
  verbs:
    - "*"

# Core Kubernetes resources for debugging
- apiGroups:
    - ''
  resources:
    - 'pods'
    - 'nodes'
    - 'namespaces'
    - 'services'
    - 'endpoints'
    - 'componentstatuses'
    - 'events'
  verbs:
    - "*"

# Access to logs and exec for troubleshooting
- apiGroups:
    - ''
  resources:
    - 'pods/log'
    - 'pods/exec'
  verbs:
    - "*"

# Access to networking resources for troubleshooting
- apiGroups:
    - 'networking.k8s.io'
  resources:
    - '*'
  verbs:
    - "*"

# Access to apiextensions for CRD inspection
- apiGroups:
    - 'apiextensions.k8s.io'
  resources:
    - 'customresourcedefinitions'
  verbs:
    - "*"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kagent.fullname" . }}-cilium-debug-rolebinding
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kagent.fullname" . }}-cilium-debug-role
subjects:
  - kind: ServiceAccount
    name: {{ include "kagent.fullname" . }}
    namespace: {{ include "kagent.namespace" . }}