{{- $dot := . }}
{{- $model := index $dot.Values.providers $dot.Values.providers.default  }}
{{- if and $model.apiKeySecretRef $model.apiKey }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ $model.apiKeySecretRef | quote }}
  namespace: {{ include "kagent.namespace" . }}
  labels:
    {{- include "kagent.labels" $dot | nindent 4 }}
type: Opaque
data:
  {{ $model.apiKeySecretKey | default (printf "%s_API_KEY" $model.provider | upper) }}: {{ $model.apiKey | b64enc }}
{{- end }}