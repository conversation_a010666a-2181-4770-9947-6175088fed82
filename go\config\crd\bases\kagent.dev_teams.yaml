---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: teams.kagent.dev
spec:
  group: kagent.dev
  names:
    kind: Team
    listKind: TeamList
    plural: teams
    singular: team
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Team is the Schema for the teams API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: TeamSpec defines the desired state of Team.
            properties:
              description:
                type: string
              maxTurns:
                format: int64
                type: integer
              modelConfig:
                description: Can either be a reference to the name of a ModelConfig
                  in the same namespace as the referencing Team, or a reference to
                  the name of a ModelConfig in a different namespace in the form <namespace>/<name>
                type: string
              participants:
                description: Each Participant can either be a reference to the name
                  of an Agent in the same namespace as the referencing Team, or a
                  reference to the name of an Agent in a different namespace in the
                  form <namespace>/<name>
                items:
                  type: string
                type: array
              roundRobinTeamConfig:
                type: object
              terminationCondition:
                properties:
                  finalTextMessageTermination:
                    properties:
                      source:
                        type: string
                    required:
                    - source
                    type: object
                  maxMessageTermination:
                    description: 'ONEOF: maxMessageTermination, textMentionTermination,
                      orTermination'
                    properties:
                      maxMessages:
                        type: integer
                    required:
                    - maxMessages
                    type: object
                  orTermination:
                    properties:
                      conditions:
                        items:
                          properties:
                            maxMessageTermination:
                              properties:
                                maxMessages:
                                  type: integer
                              required:
                              - maxMessages
                              type: object
                            textMentionTermination:
                              properties:
                                text:
                                  type: string
                              required:
                              - text
                              type: object
                          type: object
                        type: array
                    required:
                    - conditions
                    type: object
                  stopMessageTermination:
                    type: object
                  textMentionTermination:
                    properties:
                      text:
                        type: string
                    required:
                    - text
                    type: object
                  textMessageTermination:
                    properties:
                      source:
                        type: string
                    required:
                    - source
                    type: object
                type: object
            required:
            - description
            - maxTurns
            - modelConfig
            - participants
            type: object
          status:
            description: TeamStatus defines the observed state of Team.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                format: int64
                type: integer
            required:
            - conditions
            - observedGeneration
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
