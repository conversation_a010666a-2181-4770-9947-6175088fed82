{"CreatedAt": "0001-01-01T00:00:00Z", "DeletedAt": null, "ID": 0, "UpdatedAt": "0001-01-01T00:00:00Z", "component": {"component_type": "team", "component_version": 0, "config": {"participants": [{"component_type": "agent", "component_version": 0, "config": {"description": "A basic test agent", "model_client": {"component_type": "model", "component_version": 0, "config": {"api_key": "sk-test-api-key", "max_tokens": 1024, "model": "gpt-4o", "stream_options": {"include_usage": true}, "temperature": 0.7, "top_p": 0.95}, "description": "", "label": "", "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "version": 1}, "model_client_stream": true, "model_context": {"component_type": "chat_completion_context", "component_version": 0, "config": {}, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "provider": "autogen_core.model_context.UnboundedChatCompletionContext", "version": 1}, "name": "test__NS__basic_agent", "reflect_on_tool_use": false, "system_message": "You are a helpful assistant.", "tool_call_summary_format": "\nTool: \n{tool_name}\n\nArguments:\n\n{arguments}\n\nResult: \n{result}\n", "tools": null}, "description": "A basic test agent", "label": "", "provider": "autogen_agentchat.agents.AssistantAgent", "version": 1}], "termination_condition": {"component_type": "termination", "component_version": 0, "config": {"source": "test__NS__basic_agent"}, "description": "", "label": "", "provider": "kagent.conditions.FinalTextMessageTermination", "version": 1}}, "description": "A basic test agent", "label": "test/basic-agent", "provider": "autogen_agentchat.teams.RoundRobinGroupChat", "version": 1}, "name": "test/basic-agent"}