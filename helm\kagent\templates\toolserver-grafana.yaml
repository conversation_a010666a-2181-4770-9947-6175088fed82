apiVersion: kagent.dev/v1alpha1
kind: ToolServer
metadata:
  name: mcp-grafana
  namespace: kagent
spec:
  config:
    stdio:
      command: /app/python/bin/mcp-grafana
      args:
        - -t
        - stdio
        - debug
      readTimeoutSeconds: 30
      envFrom:
      - name: "GRAFANA_URL"
        value: {{ .Values.tools.grafana.url | quote }}
      - name: "GRAFANA_API_KEY"
        valueFrom:
          type: Secret
          key: "grafana"
          valueRef: {{ include "kagent.fullname" . }}-toolserver-secret
  description: ""
