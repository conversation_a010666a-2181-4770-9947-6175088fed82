{"CreatedAt": "0001-01-01T00:00:00Z", "DeletedAt": null, "ID": 0, "UpdatedAt": "0001-01-01T00:00:00Z", "component": {"component_type": "team", "component_version": 0, "config": {"participants": [{"component_type": "agent", "component_version": 0, "config": {"description": "An agent using Ollama local model", "model_client": {"component_type": "model", "component_version": 0, "config": {"follow_redirects": true, "headers": {"User-Agent": "kagent/1.0"}, "host": "http://localhost:11434", "model": "llama3.2:latest", "model_info": {"family": "llama", "function_calling": false, "json_output": false, "multiple_system_messages": false, "structured_output": false, "vision": false}, "options": {"num_ctx": "2048", "temperature": "0.8", "top_p": "0.9"}, "timeout": 0}, "description": "", "label": "", "provider": "autogen_ext.models.ollama.OllamaChatCompletionClient", "version": 1}, "model_client_stream": true, "model_context": {"component_type": "chat_completion_context", "component_version": 0, "config": {}, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "provider": "autogen_core.model_context.UnboundedChatCompletionContext", "version": 1}, "name": "test__NS__ollama_agent", "reflect_on_tool_use": false, "system_message": "You are a helpful AI assistant running locally via Ollama.", "tool_call_summary_format": "\nTool: \n{tool_name}\n\nArguments:\n\n{arguments}\n\nResult: \n{result}\n", "tools": null}, "description": "An agent using Ollama local model", "label": "", "provider": "autogen_agentchat.agents.AssistantAgent", "version": 1}], "termination_condition": {"component_type": "termination", "component_version": 0, "config": {"source": "test__NS__ollama_agent"}, "description": "", "label": "", "provider": "kagent.conditions.FinalTextMessageTermination", "version": 1}}, "description": "An agent using Ollama local model", "label": "test/ollama-agent", "provider": "autogen_agentchat.teams.RoundRobinGroupChat", "version": 1}, "name": "test/ollama-agent"}