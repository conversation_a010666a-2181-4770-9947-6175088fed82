from typing import Dict, TypedDict

from autogen_core.models import ModelInfo as BaseModelInfo


class ModelInfo(BaseModelInfo):
    """Model information for Cerebras models."""
    pass


class ModelInfoDict(TypedDict, total=False):
    """Type definition for model info dictionary."""
    vision: bool
    function_calling: bool
    json_output: bool
    family: str
    structured_output: bool
    multiple_system_messages: bool


__all__ = ["ModelInfo", "ModelInfoDict"]
