# Default values for kagent
replicaCount: 1

global:
  tag: ""

# https://kagent.dev/docs/getting-started/configuring-providers
providers:
  default: openAI
  openAI:
    provider: OpenAI
    model: "gpt-4.1-mini"
    apiKeySecretRef: kagent-openai
    apiKeySecretKey: OPENAI_API_KEY
    apiKey: ""
  ollama:
    provider: Ollama
    model: "llama3.2"
    config:
      host: host.docker.internal:11434
  anthropic:
    provider: Anthropic
    model: "claude-3-sonnet-20240229"
    apiKeySecretRef: kagent-anthropic
    apiKeySecretKey: ANTHROPIC_API_KEY
    apiKey: ""
  azureOpenAI:
    provider: AzureOpenAI
    model: "gpt-4.1-mini"
    apiKeySecretRef: kagent-azure-openai
    apiKeySecretKey: AZUREOPENAI_API_KEY
    apiKey: ""
    config:
      apiVersion: "2023-05-15"
      azureAdToken: ""
      azureDeployment: ""
      azureEndpoint: ""

database:
  type: sqlite
  sqlite:
    databaseName: kagent.db
  postgres:
    url: postgres://postgres:<EMAIL>:5432/postgres

# -- Node taints which will be tolerated for `Pod` [scheduling](https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/).
tolerations: []

# -- Node labels to match for `Pod` [scheduling](https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/).
nodeSelector: {}

controller:
  loglevel: "info"

  # -- Namespaces the controller should watch.
  # If empty, the controller will watch ONLY release namespace.
  # @default -- [] (watches release namespace)
  watchNamespaces: []
  #  - watch-ns-1
  #  - watch-ns-2

  image:
    registry: cr.kagent.dev
    repository: kagent-dev/kagent/controller
    tag: "" # Will default to global, then Chart version
    pullPolicy: IfNotPresent
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 512Mi
  env: [] # Additional environment variables for the controller can be added here

tools:
  loglevel: "debug"
  image:
    registry: ghcr.io
    repository: kagent-dev/kagent/tools
    tag: "0.0.9"
    pullPolicy: IfNotPresent
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 512Mi
  prometheus:
    url: "prometheus.kagent.svc.cluster.local:9090"
    username: ""
    password: ""
  grafana: # kubectl port-forward svc/grafana 3000:3000
    url: "http://grafana.kagent.svc.cluster.local:3000"
    apiKey: ""

# additional tools can be added here
mcp:
  doc2vec:
    enabled: true
    image:
      registry: ghcr.io
      repository: kagent-dev/doc2vec/mcp
      tag: 1.1.10
      pullPolicy: IfNotPresent
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
    env: [] # Additional environment variables for the doc2vec can be added here

app:
  loglevel: "info"
  image:
    registry: cr.kagent.dev
    repository: kagent-dev/kagent/app
    tag: "" # Will default to global, then Chart version
    pullPolicy: IfNotPresent
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  env: [] # Additional environment variables for the app can be added here

ui:
  image:
    registry: cr.kagent.dev
    repository: kagent-dev/kagent/ui
    tag: "" # Will default to global, then Chart version
    pullPolicy: IfNotPresent
  resources:
    requests:
      cpu: 100m
      memory: 256Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  env: [] # Additional environment variables for the ui can be added here

service:
  type: ClusterIP
  ports:
    ui:
      port: 80
      targetPort: 8080
    app:
      port: 8081
      targetPort: 8081
    controller:
      port: 8083
      targetPort: 8083
    tools:
      port: 8084
      targetPort: 8084
    querydoc:
      port: 8085
      targetPort: 8085

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

# -- Override the namespace
# @default -- `.Release.Namespace`
namespaceOverride: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

otel:
  tracing:
    enabled: false
    exporter:
      otlp:
        endpoint: http://host.docker.internal:4317
        timeout: 15
        insecure: true

k8s-agent:
  enabled: true

kgateway-agent:
  enabled: true

istio-agent:
  enabled: true

promql-agent:
  enabled: true

observability-agent:
  enabled: true

argo-rollouts-agent:
  enabled: true

helm-agent:
  enabled: true

cilium-policy-agent:
  enabled: true

cilium-manager-agent:
  enabled: true

cilium-debug-agent:
  enabled: true
