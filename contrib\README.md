# Contrib Directory

This directory contains community and extension contributions to the kagent project. Each subfolder is organized by contribution type or integration target.

## Structure

- `agents/` — Community and third-party agent integrations.
  - `github/` —  Agent GitHub.
- `integration/` — Integrations with external systems.
  - `kgateway/` — Integration with kgateway.
- `memory/` — Memory backends and related extensions.
  - `supabase/` — Supabase-based memory backend.
- `tools/` — Additional tools, scripts, or utilities contributed by the community.

## Contributing

To add your own contribution:
1. Fork the repository and create a new branch.
2. Add your code or integration in the appropriate subdirectory.
3. Include a README.md in your subdirectory describing your contribution.
4. Submit a pull request with a clear description of your changes.

For more details, see the main `CONTRIBUTION.md` in the project root.

---

If you have questions or need help, please open an issue or contact the maintainers.

