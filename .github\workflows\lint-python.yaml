name: Lint Python Code

on:
  pull_request:
    paths:
      - '**/*.py'  # Only trigger on changes to Python files
      - 'python/pyproject.toml'
      - 'python/uv.lock'

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Install python
        run: uv python install 3.12

      - name: Sync dependencies
        working-directory: ./python
        run: |
          export GIT_LFS_SKIP_SMUDGE=1
          uv sync --all-extras

      - name: Run ruff linter
        working-directory: ./python
        run: uv run ruff check

      - name: Run ruff format check
        working-directory: ./python
        run: |
          if ! uv run ruff format --diff .; then
            echo "Ruff formatting issues detected. Please run 'uv run ruff format' locally to fix formatting issues."
            exit 1
          fi
