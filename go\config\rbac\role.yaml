---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - agent.kagent.dev
  resources:
  - toolservers
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - agent.kagent.dev
  resources:
  - toolservers/finalizers
  verbs:
  - update
- apiGroups:
  - agent.kagent.dev
  resources:
  - toolservers/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - kagent.dev
  resources:
  - agents
  - memories
  - modelconfigs
  - teams
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - kagent.dev
  resources:
  - agents/finalizers
  - memories/finalizers
  - modelconfigs/finalizers
  - teams/finalizers
  verbs:
  - update
- apiGroups:
  - kagent.dev
  resources:
  - agents/status
  - memories/status
  - modelconfigs/status
  - teams/status
  verbs:
  - get
  - patch
  - update
