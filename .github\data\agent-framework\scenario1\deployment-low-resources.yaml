apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: deployment-low-resources
spec:
  description: Insufficient resources that will cause an OOM kill
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v3' service. 
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch deployment mysql-v1 --context ${CLUSTER_CTX} -p '{"spec":{"template":{"spec":{"containers":[{"name":"mysql","resources":{"limits":{"memory":"10Mi"}}}]}}}}'
