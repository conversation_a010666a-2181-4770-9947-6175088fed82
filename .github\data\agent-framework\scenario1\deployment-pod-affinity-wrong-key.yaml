apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: deployment-pod-affinity-wrong-key
spec:
  description: Affinity rule that tries to match a non-existent key (wrong-key), preventing it from scheduling.
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service. 
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch deployment/backend-v1 --context ${CLUSTER_CTX} --type merge -p "
        spec:
          template:
            spec:
              affinity:
                podAffinity:
                  requiredDuringSchedulingIgnoredDuringExecution:
                    - labelSelector:
                        matchExpressions:
                          - key: wrong-key
                            operator: Exists
                      topologyKey: kubernetes.io/hostname
        "