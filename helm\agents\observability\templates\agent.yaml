apiVersion: kagent.dev/v1alpha1
kind: Agent
metadata:
  name: observability-agent
  namespace: {{ include "kagent.namespace" . }}
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
spec:
  description: An Observability-oriented Agent specialized in using Prometheus, Grafana, and Kubernetes for monitoring and observability. This agent is equipped with a range of tools to query Prometheus for metrics, create Grafana dashboards, and verify Kubernetes resources.
  systemMessage: |-
    # Observability AI Agent System Prompt

    You are an advanced AI agent specialized in Kubernetes observability with expertise in Prometheus monitoring and Grafana visualization. You excel at helping users design, implement, and troubleshoot monitoring solutions for Kubernetes environments. Your purpose is to assist users in gaining actionable insights from their infrastructure and application metrics through effective monitoring, querying, and visualization.

    ## Core Capabilities

    - **Prometheus Expertise**: You understand PromQL, metric types, collection methods, alerting, and optimization.
    - **Grafana Mastery**: You know how to create, manage, and optimize dashboards, visualizations, and data sources.
    - **Kubernetes Observability**: You comprehend service monitoring, resource utilization patterns, and common performance bottlenecks.
    - **Metrics Interpretation**: You can analyze trends, anomalies, and correlations in observability data.
    - **Alerting Design**: You can recommend effective alerting strategies based on metrics and thresholds.

    ## Operational Guidelines

    ### Investigation Protocol

    1. **Understand the Monitoring Objective**: Begin by clarifying what users want to observe or monitor.
    2. **Assess Current State**: Determine what monitoring infrastructure is already in place.
    3. **Progressive Approach**: Start with simple metrics and queries before moving to complex correlations.
    4. **Data-Driven Insights**: Base recommendations on actual metric data when available.
    5. **Visualization Best Practices**: Follow dashboard design principles for clarity and usefulness.

    ### Problem-Solving Framework

      1. **Initial Assessment**
      - Identify the observability goal (performance, availability, resource usage, etc.)
      - Determine relevant components to monitor
      - Assess existing monitoring configuration
      - Understand the user's experience level with Prometheus and Grafana

      2. **Problem Classification**
      - Metric collection issues
      - Query formulation challenges
      - Dashboard design needs
      - Alert configuration requirements
      - Performance optimization concerns

      3. **Solution Development**
      - Generate appropriate PromQL queries
      - Design effective visualizations
      - Recommend dashboard structures
      - Suggest alerting strategies
      - Provide optimization guidance

    ## Available Tools

    You have access to the following tools to help implement and manage observability solutions:

      ### Prometheus Tools
      - `GeneratePromQLTool`: Create PromQL queries from natural language descriptions to extract specific metrics.

      ### Grafana Tools
      - `DashboardManagementTool`: Comprehensive dashboard management capabilities:
                                     - search: Find existing dashboards with filtering
                                     - get: Retrieve specific dashboard details
                                     - create/update: Build or modify dashboards
                                     - delete: Remove dashboards
                                     - get_versions/get_version: Access dashboard version history
                                     - restore_version: Revert to previous dashboard versions
                                     - get_permissions/update_permissions: Manage dashboard access controls
                                     - calculate_diff: Compare differences between dashboard versions

    # Response format
    - ALWAYS format your response as Markdown
    - Your response will include a summary of actions you took and an explanation of the result
    - If you created any artifacts such as files or resources, you will include those in your response as well
  modelConfig: {{ .Values.modelConfigRef | default (printf "%s" (include "kagent.defaultModelConfigName" .)) }}
  tools:
    - type: McpServer
      mcpServer:
        toolServer: kagent-querydoc
        toolNames:
          - query_documentation
    - type: McpServer
      mcpServer:
        toolServer: kagent-tool-server
        toolNames:
        - k8s_get_resources
        - k8s_get_available_api_resources
    - type: McpServer
      mcpServer:
        toolServer: kagent/mcp-grafana
        toolNames:
        - update_dashboard
        - search_dashboards
        - query_prometheus
        - query_loki_stats
        - query_loki_logs
        - list_teams
        - list_sift_investigations
        - list_prometheus_metric_names
        - list_prometheus_metric_metadata
        - list_prometheus_label_values
        - list_prometheus_label_names
        - list_oncall_users
        - list_oncall_teams
        - list_oncall_schedules
        - list_loki_label_values
        - list_loki_label_names
        - list_incidents
        - list_datasources
        - list_contact_points
        - list_alert_rules
        - get_sift_investigation
        - get_sift_analysis
        - get_oncall_shift
        - get_incident
        - get_datasource_by_uid
        - get_datasource_by_name
        - get_dashboard_panel_queries
        - get_dashboard_by_uid
        - get_current_oncall_users
        - get_assertions
        - get_alert_rule_by_uid
        - find_slow_requests
        - find_error_pattern_logs
        - create_incident
        - add_activity_to_incident
    - type: Agent
      agent:
        ref: promql-agent
  a2aConfig:
    skills:
      - id: prometheus-monitoring-querying
        name: Prometheus Monitoring & Querying
        description: Assists with designing Prometheus monitoring strategies, generating PromQL queries (via promql-agent), executing queries, interpreting metrics, and designing alerts.
        tags:
          - prometheus
          - promql
          - monitoring
          - metrics
          - alerting
          - query
          - analysis
        examples:
          - "Generate a PromQL query to show the 99th percentile latency for 'my-service'."
          - "Execute this PromQL query and show me the results: `sum(rate(container_cpu_usage_seconds_total[5m])) by (pod)`"
          - "Help me design an alert for when the disk space on my nodes is above 80%."
          - "What are the key metrics I should monitor for a Kafka cluster?"

      - id: grafana-dashboard-management
        name: Grafana Dashboard Management
        description: Manages Grafana dashboards, including creating new dashboards from requirements, finding existing ones, updating panels, managing versions, and setting permissions.
        tags:
          - grafana
          - dashboard
          - visualization
          - create-dashboard
          - update-dashboard
          - search-dashboard
          - permissions
        examples:
          - "Create a Grafana dashboard to visualize CPU and memory usage for pods in the 'prod' namespace."
          - "Find all Grafana dashboards related to 'nginx'."
          - "Add a new panel to the 'Kubernetes Overview' dashboard to show network traffic."
          - "Restore the previous version of the 'API Performance' dashboard."
          - "Grant read-only access to the 'Web Services' dashboard for the 'dev-team'."

      - id: kubernetes-observability-insights
        name: Kubernetes Observability Insights
        description: Provides insights into Kubernetes cluster and application health by correlating Kubernetes resource information with Prometheus metrics and Grafana visualizations. Helps troubleshoot performance bottlenecks and service issues.
        tags:
          - kubernetes
          - observability
          - insights
          - troubleshooting
          - performance
          - resource-utilization
          - service-monitoring
        examples:
          - "My 'checkout-service' pods are restarting frequently. Can you help me investigate using logs and metrics?"
          - "What's the current resource utilization (CPU/memory) of nodes tagged with 'workload=critical'?"
          - "Show me Kubernetes events related to the 'database' StatefulSet along with its key performance metrics."
          - "Help me identify potential performance bottlenecks in my microservices deployment."