###
# Qdrant Database Deployment and Service Configuration
# This configuration deploys Qdrant, a vector search engine, in a Kubernetes cluster.
# TODO: add MCP https://github.com/qdrant/mcp-server-qdrant
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qdrant
spec:
  replicas: 1
  selector:
    matchLabels:
      app: qdrant
  template:
    metadata:
      labels:
        app: qdrant
    spec:
      containers:
        - name: qdrant
          image: qdrant/qdrant
          ports:
            - containerPort: 6333
            - containerPort: 6334
---
apiVersion: v1
kind: Service
metadata:
  name: qdrant
spec:
  selector:
    app: qdrant
  ports:
    - protocol: TCP
      name: http
      port: 6333
      targetPort: 6333
    - protocol: TCP
      name: grpc
      port: 6334
      targetPort: 6334
  type: ClusterIP
---
