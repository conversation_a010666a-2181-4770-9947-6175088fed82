apiVersion: v1
kind: Service
metadata:
  name: {{ include "kagent.fullname" . }}
  namespace: {{ include "kagent.namespace" . }}
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.ports.ui.port }}
      targetPort: {{ .Values.service.ports.ui.targetPort }}
      protocol: TCP
      name: ui
    - port: {{ .Values.service.ports.app.port }}
      targetPort: {{ .Values.service.ports.app.targetPort }}
      protocol: TCP
      name: app
    - port: {{ .Values.service.ports.controller.port }}
      targetPort: {{ .Values.service.ports.controller.targetPort }}
      protocol: TCP
      name: controller
    - port: {{ .Values.service.ports.tools.port }}
      targetPort: {{ .Values.service.ports.tools.targetPort }}
      protocol: TCP
      name: tools
    - port: {{ .Values.service.ports.querydoc.port }}
      targetPort: {{ .Values.service.ports.querydoc.targetPort }}
      protocol: TCP
      name: querydoc
  selector:
    {{- include "kagent.selectorLabels" . | nindent 4 }}
