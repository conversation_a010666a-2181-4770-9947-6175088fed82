apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: configmap-missing-key
spec:
  description: Application Expects a Key That Does NOT Exist
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl --context ${CLUSTER_CTX} apply -f - <<EOF
        apiVersion: v1
        kind: ConfigMap
        metadata:
          name: backend-v1-configmap
          namespace: default
        data:
          CONFIG_PATH: "/etc/config"  
        EOF
        kubectl patch deployment/backend-v1 --context ${CLUSTER_CTX} -p "
        spec:
          template:
            spec:
              containers:
              - name: backend
                env:
                - name: APP_CONFIG
                  valueFrom:
                    configMapKeyRef:
                      name: backend-v1-configmap
                      key: APP_CONFIG
        "
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v1"