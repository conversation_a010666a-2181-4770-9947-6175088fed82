---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: memories.kagent.dev
spec:
  group: kagent.dev
  names:
    kind: Memory
    listKind: MemoryList
    plural: memories
    singular: memory
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.provider
      name: Provider
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Memory is the Schema for the memories API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: MemorySpec defines the desired state of Memory.
            properties:
              apiKeySecretKey:
                description: The key in the secret that contains the API key
                type: string
              apiKeySecretRef:
                description: |-
                  The reference to the secret that contains the API key. Can either be a reference to the name of a secret in the same namespace as the referencing Memory,
                  or a reference to the name of a secret in a different namespace in the form <namespace>/<name>
                type: string
              pinecone:
                description: The configuration for the Pinecone memory provider
                properties:
                  indexHost:
                    description: The index host to connect to
                    type: string
                  namespace:
                    description: The namespace to use for the Pinecone index. If not
                      provided, the default namespace will be used.
                    type: string
                  recordFields:
                    description: The fields to retrieve from the Pinecone index. If
                      not provided, all fields will be retrieved.
                    items:
                      type: string
                    type: array
                  scoreThreshold:
                    description: The score threshold of results to include in the
                      context. Results with a score below this threshold will be ignored.
                    type: string
                  topK:
                    description: The number of results to return from the Pinecone
                      index
                    type: integer
                required:
                - indexHost
                type: object
              provider:
                default: Pinecone
                description: The provider of the memory
                enum:
                - Pinecone
                type: string
            required:
            - provider
            type: object
          status:
            description: MemoryStatus defines the observed state of Memory.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                format: int64
                type: integer
            required:
            - conditions
            - observedGeneration
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
