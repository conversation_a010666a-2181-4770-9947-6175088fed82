apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: deployment-impossible-pod-affinity
spec:
  description: Affinity and an anti-affinity rule that contradict each other, making scheduling impossible.
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service. 
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch deployment/backend-v1 --context ${CLUSTER_CTX} --type merge -p "
        spec:
          template:
            spec:
              affinity:
                podAffinity:
                  requiredDuringSchedulingIgnoredDuringExecution:
                    - labelSelector:
                        matchLabels:
                          app: frontend
                      topologyKey: "kubernetes.io/hostname"
                podAntiAffinity:
                  requiredDuringSchedulingIgnoredDuringExecution:
                    - labelSelector:
                        matchLabels:
                          app: frontend
                      topologyKey: "kubernetes.io/hostname"
        "