apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: serviceaccount-misspelled
spec:
  description: ServiceAccount not found because a name mismatch
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl --context ${CLUSTER_CTX} apply -f - <<EOF
        apiVersion: v1
        kind: ServiceAccount
        metadata:
          name: backend-v1
          namespace: default
        EOF
        kubectl patch deployment/backend-v1 --context ${CLUSTER_CTX} --type merge -p "
        spec:
          template:
            spec:
              serviceAccountName: backend-v1-sa
        "
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v1"