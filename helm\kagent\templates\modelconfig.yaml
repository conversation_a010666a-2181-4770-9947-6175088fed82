{{- $dot := . }}
{{- $defaultProfider := .Values.providers.default | default "openAI" }}
{{- $model := index .Values.providers $defaultProfider }}
{{- if hasKey .Values.providers  $defaultProfider | not }}
{{- fail  (printf "Provider key=%s is not found under .Values.providers" $defaultProfider)  }}
{{- end }}
---
apiVersion: kagent.dev/v1alpha1
kind: ModelConfig
metadata:
  name: {{ include "kagent.defaultModelConfigName" $dot | quote }}
  namespace: {{ include "kagent.namespace" . }}
  labels:
    {{- include "kagent.labels" $dot | nindent 4 }}
spec:
  {{- with $model }}
  provider: {{ .provider | quote }}
  model: {{ .model | quote }}
  {{- if $model.apiKeySecretRef }}
  apiKeySecretRef: {{.apiKeySecretRef}}
  {{- end }}
  {{- if $model.apiKeySecretKey }}
  apiKeySecretKey: {{.apiKeySecretKey}}
  {{- end }}
  {{- if hasKey $model "defaultHeaders" }}
  defaultHeaders:
    {{- toYaml $model.defaultHeaders | nindent 4 }}
  {{- end }}
  {{ $dot.Values.providers.default }}:
  {{- toYaml $model.config | nindent 4 }}
  {{- end}}