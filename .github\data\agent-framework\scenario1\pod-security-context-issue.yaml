apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: pod-security-context-issue
spec:
  description: Pod Security Context prevents container from starting
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch deployment/backend-v1 --context ${CLUSTER_CTX} -p "
        spec:
          template:
            spec:
              securityContext:
                runAsNonRoot: true
              initContainers:
              - name: backend-init
                image: bitnami/kubectl
                command:
                - ls
                - /root
        "
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v1"