apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: pod-limit-range-exceeded
spec:
  description: Pod exceeds namespace LimitRange, pod cannot be created
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see backend-v3 pods failing to create due to limit range.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl apply --context ${CLUSTER_CTX} -f - <<EOF
        apiVersion: v1
        kind: LimitRange
        metadata:
          name: mem-limit-range
          namespace: default
        spec:
          limits:
          - default:
              memory: 100Mi
            type: Container
        EOF
        kubectl patch deployment backend-v3 --context ${CLUSTER_CTX} -p '{"spec":{"template":{"spec":{"containers":[{"name":"backend","resources":{"requests":{"memory":"200Mi"}}}]}}}}'
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v3"