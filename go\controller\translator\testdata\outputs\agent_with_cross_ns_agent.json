{"CreatedAt": "0001-01-01T00:00:00Z", "DeletedAt": null, "ID": 0, "UpdatedAt": "0001-01-01T00:00:00Z", "component": {"component_type": "team", "component_version": 0, "config": {"participants": [{"component_type": "agent", "component_version": 0, "config": {"description": "An agent that uses another agent from a different namespace", "model_client": {"component_type": "model", "component_version": 0, "config": {"api_key": "sk-test-api-key", "model": "gpt-3.5-turbo", "stream_options": {"include_usage": true}}, "description": "", "label": "", "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "version": 1}, "model_client_stream": true, "model_context": {"component_type": "chat_completion_context", "component_version": 0, "config": {}, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "provider": "autogen_core.model_context.UnboundedChatCompletionContext", "version": 1}, "name": "test_consumer__NS__agent_with_cross_ns_agent", "reflect_on_tool_use": false, "system_message": "You are an assistant that can delegate to other agents.", "tool_call_summary_format": "\nTool: \n{tool_name}\n\nArguments:\n\n{arguments}\n\nResult: \n{result}\n", "tools": [{"component_type": "tool", "component_version": 0, "config": {"description": "A provider agent with vector memory", "name": "test_provider__NS__provider_agent", "team": {"component_type": "team", "component_version": 0, "config": {"participants": [{"component_type": "agent", "component_version": 0, "config": {"description": "A provider agent with vector memory", "model_client": {"component_type": "model", "component_version": 0, "config": {"api_key": "sk-test-api-key", "model": "gpt-4o"}, "description": "", "label": "", "provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "version": 1}, "model_client_stream": false, "model_context": {"component_type": "chat_completion_context", "component_version": 0, "config": {}, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "provider": "autogen_core.model_context.UnboundedChatCompletionContext", "version": 1}, "name": "test_provider__NS__provider_agent", "reflect_on_tool_use": false, "system_message": "You are a provider assistant with access to memory.", "tool_call_summary_format": "\nTool: \n{tool_name}\n\nArguments:\n\n{arguments}\n\nResult: \n{result}\n", "tools": null}, "description": "A provider agent with vector memory", "label": "", "provider": "autogen_agentchat.agents.AssistantAgent", "version": 1}], "termination_condition": {"component_type": "termination", "component_version": 0, "config": {"source": "test_provider__NS__provider_agent"}, "description": "", "label": "", "provider": "kagent.conditions.FinalTextMessageTermination", "version": 1}}, "description": "A provider agent with vector memory", "label": "test-provider/provider-agent", "provider": "autogen_agentchat.teams.RoundRobinGroupChat", "version": 1}}, "description": "", "label": "", "provider": "autogen_agentchat.tools.TeamTool", "version": 1}]}, "description": "An agent that uses another agent from a different namespace", "label": "", "provider": "autogen_agentchat.agents.AssistantAgent", "version": 1}], "termination_condition": {"component_type": "termination", "component_version": 0, "config": {"source": "test_consumer__NS__agent_with_cross_ns_agent"}, "description": "", "label": "", "provider": "kagent.conditions.FinalTextMessageTermination", "version": 1}}, "description": "An agent that uses another agent from a different namespace", "label": "test-consumer/agent-with-cross-ns-agent", "provider": "autogen_agentchat.teams.RoundRobinGroupChat", "version": 1}, "name": "test-consumer/agent-with-cross-ns-agent"}