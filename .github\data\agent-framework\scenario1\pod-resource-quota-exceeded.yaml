apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: pod-resource-quota-exceeded
spec:
  description: Namespace resource quota is exceeded, pod cannot be created
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see backend-v2 pods stuck in pending state due to quota.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl apply --context ${CLUSTER_CTX} -f - <<EOF
        apiVersion: v1
        kind: ResourceQuota
        metadata:
          name: pod-quota
          namespace: default
        spec:
          hard:
            pods: "1"
        EOF
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v2"