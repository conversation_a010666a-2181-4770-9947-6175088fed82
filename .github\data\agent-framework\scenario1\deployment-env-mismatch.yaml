apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: deployment-env-mismatch
spec:
  description: Update MySQL password but not the apps using it
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v3' service. 
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl --context ${CLUSTER_CTX} set env deployment/mysql-v1 MYSQL_ROOT_PASSWORD=mynewpassword
        kubectl --context ${CLUSTER_CTX} delete pod -l app=mysql,version=v1 --wait=false