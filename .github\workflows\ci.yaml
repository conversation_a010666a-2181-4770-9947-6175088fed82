name: CI Build

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

env:
  # Cache key components for better organization
  CACHE_KEY_PREFIX: kagent-v2
  BRANCH_CACHE_KEY: ${{ github.head_ref || github.ref_name }}
  # Consistent builder configuration
  BUILDX_BUILDER_NAME: kagent-builder-v0.23.0
  BUILDX_VERSION: v0.23.0
jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: Generate cache keys
        id: cache-key
        run: |
          # Generate branch-based cache key
          BRANCH_KEY="${CACHE_KEY_PREFIX}-${BRANCH_CACHE_KEY}"
          echo "key=${BRANCH_KEY}" >> $GITHUB_OUTPUT
          echo "::notice title=Cache Key::Branch: ${BRANCH_KEY}"
  test-e2e:
    needs:
      - setup
    env:
      VERSION: v0.0.1-test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/amd64,linux/arm64
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          name: ${{ env.BUILDX_BUILDER_NAME }}
          version: ${{ env.BUILDX_VERSION }}
          platforms: linux/amd64,linux/arm64
          use: 'true'

      - name: Set up Helm
        uses: azure/setup-helm@v4.2.0
        with:
          version: v3.17.0

      - name: Create k8s Kind Cluster
        uses: helm/kind-action@v1
        with:
          cluster_name: kagent

      # This allows us to not rely on port-forwarding which is flaky
      - name: Setup Metallb
        run: |
          bash scripts/kind/setup-metallb.sh

      - name: Install Kagent
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          BUILDX_BUILDER_NAME: ${{ env.BUILDX_BUILDER_NAME }}
          DOCKER_BUILD_ARGS: >-
            --cache-from=type=gha,scope=${{ needs.setup.outputs.cache-key }}-e2e
            --cache-from=type=gha,scope=${{ env.CACHE_KEY_PREFIX }}-main-e2e
            --cache-to=type=gha,scope=${{ needs.setup.outputs.cache-key }}-e2e,mode=max
            --platform=linux/amd64
            --load
        run: |
          echo "Cache key: ${{ needs.setup.outputs.cache-key }}"
          make helm-install
          kubectl wait --for=condition=Accepted  agents.kagent.dev -n kagent --all --timeout=60s

      - name: Run e2e tests
        run: |
          export KAGENT_API_URL="http://$(kubectl get svc -n kagent kagent -o jsonpath='{.status.loadBalancer.ingress[0].ip}'):8083/api"
          echo $KAGENT_API_URL
          cd go
          go test -v -run ^TestInvokeAPI$ github.com/kagent-dev/kagent/go/test/e2e

  go-unit-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.24"
          cache: true
          cache-dependency-path: go/go.sum

      - name: Run cmd/main.go tests
        working-directory: go
        run: |
          go test -skip 'TestInvokeAPI|TestE2E|TestAutogenClient' -v ./...

  helm-unit-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Helm
        uses: azure/setup-helm@v4.2.0
        with:
          version: v3.17.0
      # Install unittest plugin
      - name: Install unittest plugin
        run: |
          helm plugin install https://github.com/helm-unittest/helm-unittest

      - name: Chart init
        run: |
          make helm-version

      - name: Run helm unit tests
        run: |
          helm unittest helm/kagent

  ui-tests:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: ui/package-lock.json

      - name: Install dependencies
        working-directory: ./ui
        run: npm ci

      - name: Run tests
        working-directory: ./ui
        run: npm run test

  # This job builds the Docker images for the controller, UI, app, and CLI on arm64.
  build:
    needs: setup
    env:
      VERSION: v0.0.1-test
    strategy:
      matrix:
        image:
          - controller
          - ui
          - app
          - cli
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/amd64,linux/arm64
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          name: ${{ env.BUILDX_BUILDER_NAME }}
          platforms: linux/amd64,linux/arm64
          version: ${{ env.BUILDX_VERSION }}
          use: 'true'
      - name: Run make build
        env:
          BUILDX_BUILDER_NAME: ${{ env.BUILDX_BUILDER_NAME }}
          DOCKER_BUILD_ARGS: >-
            --cache-from=type=gha,scope=${{ needs.setup.outputs.cache-key }}-${{ matrix.image }}
            --cache-from=type=gha,scope=${{ env.CACHE_KEY_PREFIX }}-main-${{ matrix.image }}
            --cache-to=type=gha,scope=${{ needs.setup.outputs.cache-key }}-${{ matrix.image }},mode=max
            --platform=linux/amd64,linux/arm64
            --output=type=tar,dest=/dev/null
          DOCKER_REPO: "${{ github.repository_owner }}/kagent"
          DOCKER_BUILDER: "docker buildx"
        run: make build-${{ matrix.image }}
        working-directory: ./

