apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: pvc-wrong-accessmode
spec:
  description: PV Exists but Incorrect Access Mode
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v3' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl --context ${CLUSTER_CTX} scale --replicas=0 deploy/mysql-v1
        kubectl --context ${CLUSTER_CTX} scale --replicas=0 deploy/backend-v3
        kubectl --context ${CLUSTER_CTX} delete pvc mysql-pvc
        kubectl --context ${CLUSTER_CTX} apply -f - <<EOF
        apiVersion: v1
        kind: PersistentVolumeClaim
        metadata:
          name: mysql-pvc
          namespace: default
        spec:
          accessModes:
            - ReadOnlyMany
          resources:
            requests:
              storage: 1Gi
          storageClassName: standard
        EOF
        kubectl --context ${CLUSTER_CTX} scale --replicas=1 deploy/mysql-v1
        kubectl --context ${CLUSTER_CTX} scale --replicas=1 deploy/backend-v3