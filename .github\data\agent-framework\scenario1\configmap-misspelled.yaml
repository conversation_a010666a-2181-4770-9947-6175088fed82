apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: configmap-misspelled
spec:
  description: ConfigMap that is not correctly referenced in the deployment.
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v3' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch deployment backend-v3 --context ${CLUSTER_CTX} -p '{"spec":{"template":{"spec":{"volumes":[{"name":"backend-v3-code","configMap":{"name":"backend-v3-code-cm"}}]}}}}'
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v3"