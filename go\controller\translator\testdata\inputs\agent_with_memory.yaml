operation: translateAgent
targetObject: agent-with-memory
namespace: test
objects:
  - apiVersion: v1
    kind: Secret
    metadata:
      name: openai-secret
      namespace: test
    data:
      api-key: c2stdGVzdC1hcGkta2V5  # base64 encoded "sk-test-api-key"
  - apiVersion: v1
    kind: Secret
    metadata:
      name: pinecone-secret
      namespace: test
    data:
      api-key: cGluZWNvbmUtYXBpLWtleQ==  # base64 encoded "pinecone-api-key"
  - apiVersion: kagent.dev/v1alpha1
    kind: ModelConfig
    metadata:
      name: memory-model
      namespace: test
    spec:
      provider: OpenAI
      model: gpt-4o
      apiKeySecretRef: openai-secret
      apiKeySecretKey: api-key
  - apiVersion: kagent.dev/v1alpha1
    kind: Memory
    metadata:
      name: vector-memory
      namespace: test
    spec:
      provider: Pinecone
      apiKeySecretRef: pinecone-secret
      apiKeySecretKey: api-key
      pinecone:
        indexHost: "https://test-index.pinecone.io"
        topK: 5
        namespace: "test-namespace"
        recordFields: ["content", "metadata"]
        scoreThreshold: "0.7"
  - apiVersion: kagent.dev/v1alpha1
    kind: Agent
    metadata:
      name: agent-with-memory
      namespace: test
    spec:
      description: An agent with vector memory
      systemMessage: You are an assistant with access to long-term memory.
      modelConfig: memory-model
      memory:
        - vector-memory
      tools: [] 