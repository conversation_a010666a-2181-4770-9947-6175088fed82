suite: test secret creation
tests:
  - it: should create global secret when tokenSecret.value is provided
    template: templates/secret.yaml
    set:
      tokenSecret.name: "github-mcp-server-token"
      tokenSecret.value: "ghp_global_token"
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Secret
      - equal:
          path: metadata.name
          value: github-mcp-server-token
      - equal:
          path: stringData.token
          value: "Bearer ghp_global_token"

  - it: should create toolset-specific secret when tokenSecret.value is provided
    template: templates/secret.yaml
    set:
      tools.repositories.tokenSecret.value: "ghp_repo_token"
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: Secret
      - equal:
          path: metadata.name
          value: RELEASE-NAME-github-mcp-server-repositories-token
      - equal:
          path: stringData.token
          value: "Bearer ghp_repo_token"

  - it: should create multiple secrets for different toolsets
    template: templates/secret.yaml
    set:
      tools.repositories.tokenSecret.value: "ghp_repo_token"
      tools.issues.tokenSecret.value: "ghp_issues_token"
    asserts:
      - hasDocuments:
          count: 2
      - isKind:
          of: Secret

  - it: should use custom secret name when provided
    template: templates/secret.yaml
    set:
      tools.repositories.tokenSecret.value: "ghp_repo_token"
      tools.repositories.tokenSecret.name: "custom-repo-secret"
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: custom-repo-secret

  - it: should use custom key when provided
    template: templates/secret.yaml
    set:
      tools.repositories.tokenSecret.value: "ghp_repo_token"
      tools.repositories.tokenSecret.key: "github-token"
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: stringData.github-token
          value: "Bearer ghp_repo_token"
