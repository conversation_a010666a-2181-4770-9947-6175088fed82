apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: service-incorrect-port-number
spec:
  description: Service port number is incorrect, service is not reachable on expected port
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see errors reaching backend-v1.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch service/backend-v1 --context ${CLUSTER_CTX} -p "
        spec:
          ports:
            - port: 8080
              targetPort: 8081
        "