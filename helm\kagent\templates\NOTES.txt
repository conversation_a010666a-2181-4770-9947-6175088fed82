################################ To open kagent UI: ###########################################
#
#  This is a Helm chart for <PERSON><PERSON>, a Kubernetes agent.
#
#  1. Forward application port by running these commands in the terminal:
#  kubectl -n {{ include "kagent.namespace" . }} port-forward service/{{ .Release.Name }} 8001:80
#
#  2. Then visit http://127.0.0.1:8001 to use the application.
#
###############################################################################################