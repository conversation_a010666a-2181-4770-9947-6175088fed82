apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: deployment-probe-failures
spec:
  description: Readiness probe is failing
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 is not working.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`.
  steps:
    - run: |
        kubectl patch deployment frontend-v1 --context ${CLUSTER_CTX} -p '{"spec":{"template":{"spec":{"containers":[{"name":"frontend","readinessProbe":{"tcpSocket":{"port":9999}}}]}}}}'