apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: configmap-readonly
spec:
  description: A pod mounts a ConfigMap in a read-only filesystem, causing the application to fail if it tries to modify the file.
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl --context ${CLUSTER_CTX} apply -f - <<EOF
        apiVersion: v1
        kind: ConfigMap
        metadata:
          name: backend-v1-configmap
          namespace: default
        data:
          app.conf: "mode=production"
        EOF
        kubectl patch deployment/backend-v1 --context ${CLUSTER_CTX} -p "
        spec:
          template:
            spec:
              initContainers:
              - name: backend-init
                image: busybox
                volumeMounts:
                - name: config-volume
                  mountPath: "/etc/config"
                command:
                - sh
                - -c
                - echo 'mode=debug' > /etc/config/app.conf
              volumes:
              - name: config-volume
                configMap:
                  name: backend-v1-configmap
        "
        kubectl --context ${CLUSTER_CTX} delete rs -l "app=backend,version=v1"