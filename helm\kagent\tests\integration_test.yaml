suite: integration tests
templates:
  - deployment.yaml
  - service.yaml
  - serviceaccount.yaml
  - clusterrole.yaml
  - clusterrolebinding.yaml
  - secret.yaml
  - modelconfig.yaml
tests:
  - it: should have consistent naming for deployment
    template: deployment.yaml
    asserts:
      - equal:
          path: metadata.name
          value: RELEASE-NAME

  - it: should have consistent naming for service
    template: service.yaml
    asserts:
      - equal:
          path: metadata.name
          value: RELEASE-NAME

  - it: should have consistent naming for serviceaccount
    template: serviceaccount.yaml
    asserts:
      - equal:
          path: metadata.name
          value: RELEASE-NAME

  - it: should have consistent labels across all resources
    templates:
      - deployment.yaml
      - service.yaml
      - serviceaccount.yaml
      - clusterrole.yaml
      - clusterrolebinding.yaml
    asserts:
      - equal:
          path: metadata.labels["app.kubernetes.io/name"]
          value: kagent
      - equal:
          path: metadata.labels["app.kubernetes.io/instance"]
          value: RELEASE-NAME
      - equal:
          path: metadata.labels["app.kubernetes.io/managed-by"]
          value: Helm

  - it: should use consistent namespace across namespaced resources
    set:
      namespaceOverride: "custom-namespace"
    templates:
      - deployment.yaml
      - service.yaml
      - serviceaccount.yaml
    asserts:
      - equal:
          path: metadata.namespace
          value: custom-namespace

  - it: should validate complete production setup
    set:
      replicaCount: 3
      global:
        tag: "v1.0.0"
      providers:
        default: openAI
        openAI:
          apiKey: "sk-production-key"
          model: "gpt-4"
      controller:
        loglevel: "info"
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
      app:
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 2000m
            memory: 2Gi
      service:
        type: ClusterIP
      otel:
        tracing:
          enabled: true
          exporter:
            otlp:
              endpoint: "http://jaeger.monitoring.svc.cluster.local:4317"
    template: deployment.yaml
    asserts:
      - equal:
          path: spec.replicas
          value: 3
      - equal:
          path: spec.template.spec.containers[0].image
          value: cr.kagent.dev/kagent-dev/kagent/controller:v1.0.0 