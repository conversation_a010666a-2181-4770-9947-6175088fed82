{"name": "kagents-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 8001 -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.474.0", "next": "^15.3.4", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.58.1", "react-markdown": "^9.1.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/cypress": "^0.1.6", "@types/jest": "^29.5.14", "@types/node": "20.17.47", "@types/react": "19.1.4", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.1.6", "cypress": "^14.5.0", "eslint": "^9.30.0", "eslint-config-next": "15.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.10.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "5.8.3"}}