apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kagent.fullname" . }}-cilium-manager-role
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
rules:
# Access to Cilium CRDs for management and monitoring
- apiGroups:
    - 'cilium.io'
  resources:
    - '*'
  verbs:
    - "*"

# Access to core resources for pod/node management
- apiGroups:
    - ''
  resources:
    - 'pods'
    - 'nodes'
    - 'namespaces'
    - 'services'
    - 'endpoints'
    - 'componentstatuses'
  verbs:
    - "*"

# Access to logs for troubleshooting
- apiGroups:
    - ''
  resources:
    - 'pods/log'
    - 'pods/exec'
  verbs:
    - "*"

# Access to deployments and daemonsets for installation/upgrade
- apiGroups:
    - 'apps'
  resources:
    - 'deployments'
    - 'daemonsets'
    - 'statefulsets'
    - 'replicasets'
  verbs:
    - "*"

# Access to networking resources
- apiGroups:
    - 'networking.k8s.io'
  resources:
    - 'networkpolicies'
    - 'ingresses'
  verbs:
    - "*"

# Access to apiextensions for CRD management
- apiGroups:
    - 'apiextensions.k8s.io'
  resources:
    - 'customresourcedefinitions'
  verbs:
    - "*"

# Access to helm releases for installation management
- apiGroups:
    - 'helm.toolkit.fluxcd.io'
  resources:
    - 'helmreleases'
  verbs:
    - "*"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kagent.fullname" . }}-cilium-manager-rolebinding
  labels:
    {{- include "kagent.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kagent.fullname" . }}-cilium-manager-role
subjects:
  - kind: ServiceAccount
    name: {{ include "kagent.fullname" . }}
    namespace: {{ include "kagent.namespace" . }}
