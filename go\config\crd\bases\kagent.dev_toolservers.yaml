---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.1
  name: toolservers.kagent.dev
spec:
  group: kagent.dev
  names:
    kind: ToolServer
    listKind: ToolServerList
    plural: toolservers
    shortNames:
    - ts
    singular: toolserver
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ToolServer is the Schema for the toolservers API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ToolServerSpec defines the desired state of ToolServer.
            properties:
              config:
                properties:
                  sse:
                    properties:
                      headers:
                        x-kubernetes-preserve-unknown-fields: true
                      headersFrom:
                        items:
                          description: ValueRef represents a configuration value
                          properties:
                            name:
                              type: string
                            value:
                              type: string
                            valueFrom:
                              description: ValueSource defines a source for configuration
                                values from a Secret or ConfigMap
                              properties:
                                key:
                                  type: string
                                type:
                                  enum:
                                  - ConfigMap
                                  - Secret
                                  type: string
                                valueRef:
                                  description: |-
                                    The reference to the ConfigMap or Secret. Can either be a reference to a resource in the same namespace,
                                    or a reference to a resource in a different namespace in the form "namespace/name".
                                    If namespace is not provided, the default namespace is used.
                                  type: string
                              required:
                              - key
                              - type
                              type: object
                          required:
                          - name
                          type: object
                          x-kubernetes-validations:
                          - message: Exactly one of value or valueFrom must be specified
                            rule: (has(self.value) && !has(self.valueFrom)) || (!has(self.value)
                              && has(self.valueFrom))
                        type: array
                      sseReadTimeout:
                        type: string
                      timeout:
                        type: string
                      url:
                        type: string
                    required:
                    - url
                    type: object
                  stdio:
                    properties:
                      args:
                        items:
                          type: string
                        type: array
                      command:
                        type: string
                      env:
                        additionalProperties:
                          type: string
                        type: object
                      envFrom:
                        items:
                          description: ValueRef represents a configuration value
                          properties:
                            name:
                              type: string
                            value:
                              type: string
                            valueFrom:
                              description: ValueSource defines a source for configuration
                                values from a Secret or ConfigMap
                              properties:
                                key:
                                  type: string
                                type:
                                  enum:
                                  - ConfigMap
                                  - Secret
                                  type: string
                                valueRef:
                                  description: |-
                                    The reference to the ConfigMap or Secret. Can either be a reference to a resource in the same namespace,
                                    or a reference to a resource in a different namespace in the form "namespace/name".
                                    If namespace is not provided, the default namespace is used.
                                  type: string
                              required:
                              - key
                              - type
                              type: object
                          required:
                          - name
                          type: object
                          x-kubernetes-validations:
                          - message: Exactly one of value or valueFrom must be specified
                            rule: (has(self.value) && !has(self.valueFrom)) || (!has(self.value)
                              && has(self.valueFrom))
                        type: array
                      readTimeoutSeconds:
                        default: 10
                        description: Default value is 10 seconds
                        type: integer
                    required:
                    - command
                    type: object
                  streamableHttp:
                    properties:
                      headers:
                        x-kubernetes-preserve-unknown-fields: true
                      headersFrom:
                        items:
                          description: ValueRef represents a configuration value
                          properties:
                            name:
                              type: string
                            value:
                              type: string
                            valueFrom:
                              description: ValueSource defines a source for configuration
                                values from a Secret or ConfigMap
                              properties:
                                key:
                                  type: string
                                type:
                                  enum:
                                  - ConfigMap
                                  - Secret
                                  type: string
                                valueRef:
                                  description: |-
                                    The reference to the ConfigMap or Secret. Can either be a reference to a resource in the same namespace,
                                    or a reference to a resource in a different namespace in the form "namespace/name".
                                    If namespace is not provided, the default namespace is used.
                                  type: string
                              required:
                              - key
                              - type
                              type: object
                          required:
                          - name
                          type: object
                          x-kubernetes-validations:
                          - message: Exactly one of value or valueFrom must be specified
                            rule: (has(self.value) && !has(self.valueFrom)) || (!has(self.value)
                              && has(self.valueFrom))
                        type: array
                      sseReadTimeout:
                        type: string
                      terminateOnClose:
                        type: boolean
                      timeout:
                        type: string
                      url:
                        type: string
                    required:
                    - url
                    type: object
                type: object
              description:
                type: string
            required:
            - config
            - description
            type: object
          status:
            description: ToolServerStatus defines the observed state of ToolServer.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              discoveredTools:
                items:
                  properties:
                    component:
                      properties:
                        component_type:
                          type: string
                        component_version:
                          type: integer
                        config:
                          description: 'note: this implementation is due to the kubebuilder
                            limitation https://github.com/kubernetes-sigs/controller-tools/issues/636'
                          x-kubernetes-preserve-unknown-fields: true
                        description:
                          type: string
                        label:
                          type: string
                        provider:
                          type: string
                        version:
                          type: integer
                      required:
                      - component_type
                      - component_version
                      - description
                      - label
                      - provider
                      - version
                      type: object
                    name:
                      type: string
                  required:
                  - component
                  - name
                  type: object
                type: array
              observedGeneration:
                description: |-
                  INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                format: int64
                type: integer
            required:
            - conditions
            - observedGeneration
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
