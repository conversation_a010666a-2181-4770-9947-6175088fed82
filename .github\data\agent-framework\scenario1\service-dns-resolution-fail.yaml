apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: service-dns-resolution-fail
spec:
  description: Service DNS resolution fails within cluster
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see errors reaching backend-v2 due to DNS resolution issues.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl patch deployment backend-v2 --context ${CLUSTER_CTX} -p '{"spec":{"template":{"spec":{"dnsPolicy": "None", "dnsConfig": {"nameservers": ["*******"]}}}}}'