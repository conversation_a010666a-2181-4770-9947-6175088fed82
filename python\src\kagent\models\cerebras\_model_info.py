from typing import Dict

from autogen_core.models import ModelInfo

from .types import ModelInfoDict

# Cerebras AI model information based on their API documentation
# https://inference-docs.cerebras.ai/api-reference/models
_MODEL_INFO: ModelInfoDict = {
    "llama3.1-8b": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "llama3.1",
        "structured_output": True,
        "multiple_system_messages": False,
    },
    "llama-3.3-70b": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "llama-3.3",
        "structured_output": True,
        "multiple_system_messages": False,
    },
    "llama-4-scout-17b-16e-instruct": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "llama-4",
        "structured_output": True,
        "multiple_system_messages": False,
    },
    "llama-4-maverick-17b-128e": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "llama-4",
        "structured_output": True,
        "multiple_system_messages": False,
    },
    "qwen-3-32b": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "qwen-3",
        "structured_output": True,
        "multiple_system_messages": False,
    },
    "qwen-3-235b-a22b": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "qwen-3",
        "structured_output": True,
        "multiple_system_messages": False,
    },
    "deepseek-r1-distill-llama-70b": {
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "deepseek-r1",
        "structured_output": True,
        "multiple_system_messages": False,
    },
}

# Token limits for Cerebras models (estimated based on typical limits)
_MODEL_TOKEN_LIMITS: Dict[str, int] = {
    "llama3.1-8b": 128000,
    "llama-3.3-70b": 128000,
    "llama-4-scout-17b-16e-instruct": 128000,
    "llama-4-maverick-17b-128e": 128000,
    "qwen-3-32b": 128000,
    "qwen-3-235b-a22b": 128000,
    "deepseek-r1-distill-llama-70b": 128000,
}


def get_info(model: str) -> ModelInfo:
    """Get the model information for a specific model."""
    # Check for exact match first
    if model in _MODEL_INFO:
        return _MODEL_INFO[model]
    raise KeyError(f"Model '{model}' not found in model info")


def get_token_limit(model: str) -> int:
    """Get the token limit for a specific model."""
    # Check for exact match first
    if model in _MODEL_TOKEN_LIMITS:
        return _MODEL_TOKEN_LIMITS[model]
    return 128000  # Default token limit


__all__ = ["_MODEL_INFO", "_MODEL_TOKEN_LIMITS", "get_info", "get_token_limit"]
