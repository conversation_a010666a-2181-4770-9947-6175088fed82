suite: test readwrite toolservers
tests:
  - it: should create readwrite toolserver when readwrite is enabled
    template: templates/toolserver.yaml
    set:
      tokenSecretRef.name: "test-token"
      tokenSecretRef.key: "token"
      tools.repositories.readwrite: true
    asserts:
      - hasDocuments:
          count: 1
      - isKind:
          of: ToolServer
      - equal:
          path: metadata.name
          value: RELEASE-NAME-github-mcp-server-repositories
      - equal:
          path: spec.config.streamableHttp.url
          value: https://api.githubcopilot.com/mcp/x/repos
      - equal:
          path: spec.config.streamableHttp.headersFrom[0].valueFrom.valueRef
          value: test-token
      - matchRegex:
          path: spec.description
          pattern: ".*GitHub Repository related tools \\(read-write\\).*"

  - it: should create readwrite toolserver for pullRequests with correct URL
    template: templates/toolserver.yaml
    set:
      tokenSecretRef.name: "test-token"
      tokenSecretRef.key: "token"
      tools.pullRequests.readwrite: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: RELEASE-NAME-github-mcp-server-pull-requests
      - equal:
          path: spec.config.streamableHttp.url
          value: https://api.githubcopilot.com/mcp/x/pull_requests

  - it: should create readwrite toolserver for codeSecurity with correct URL
    template: templates/toolserver.yaml
    set:
      tokenSecretRef.name: "test-token"
      tokenSecretRef.key: "token"
      tools.codeSecurity.readwrite: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: RELEASE-NAME-github-mcp-server-code-security
      - equal:
          path: spec.config.streamableHttp.url
          value: https://api.githubcopilot.com/mcp/x/code_security

  - it: should create readwrite toolserver for all tools
    template: templates/toolserver.yaml
    set:
      tokenSecretRef.name: "test-token"
      tokenSecretRef.key: "token"
      tools.all.readwrite: true
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: metadata.name
          value: RELEASE-NAME-github-mcp-server-all
      - equal:
          path: spec.config.streamableHttp.url
          value: https://api.githubcopilot.com/mcp/
