suite: test toolserver-docs
templates:
  - toolserver-docs.yaml
tests:
  - it: should render toolserver-docs when provider is openAI
    set:
      providers:
        default: "openAI"
    asserts:
      - isKind:
          of: ToolServer
      - equal:
          path: metadata.name
          value: RELEASE-NAME-querydoc
      - equal:
          path: metadata.namespace
          value: NAMESPACE
      - equal:
          path: spec.description
          value: "Documentation Query Tool Server"
      - hasDocuments:
          count: 1

  - it: should have correct streamableHttp configuration when provider is openAI
    set:
      providers:
        default: "openAI"
    asserts:
      - equal:
          path: spec.config.streamableHttp.sseReadTimeout
          value: 5m0s
      - equal:
          path: spec.config.streamableHttp.timeout
          value: 30s
      - equal:
          path: spec.config.streamableHttp.url
          value: http://localhost:8085/mcp

  - it: should not have labels when provider is openAI (toolserver-docs template doesn't include common labels)
    set:
      providers:
        default: "openAI"
    asserts:
      - notExists:
          path: metadata.labels

  - it: should use custom namespace when overridden and provider is openAI
    set:
      providers:
        default: "openAI"
      namespaceOverride: "custom-namespace"
    asserts:
      - equal:
          path: metadata.namespace
          value: custom-namespace

  - it: should use custom fullname when overridden and provider is openAI
    set:
      providers:
        default: "openAI"
      fullnameOverride: "custom-kagent"
    asserts:
      - equal:
          path: metadata.name
          value: custom-kagent-querydoc

  - it: should use custom querydoc port in streamableHttp URL when provider is openAI
    set:
      providers:
        default: "openAI"
      service:
        ports:
          querydoc:
            targetPort: 9085
    asserts:
      - equal:
          path: spec.config.streamableHttp.url
          value: http://localhost:9085/mcp

  - it: should have correct APIVersion and Kind when provider is openAI
    set:
      providers:
        default: "openAI"
    asserts:
      - equal:
          path: apiVersion
          value: kagent.dev/v1alpha1
      - equal:
          path: kind
          value: ToolServer
