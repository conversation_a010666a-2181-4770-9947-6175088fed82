from typing import List, Optional

from pydantic import BaseModel, Field, SecretStr

from .types import ModelInfo


class CebrasClientConfiguration(BaseModel):
    """Configuration for Cerebras AI chat completion client."""
    
    model: str = Field(description="Name of the Cerebras model to use, e.g., 'llama3.1-8b'.")
    api_key: Optional[SecretStr] = Field(default=None, description="Cerebras API key.")
    base_url: Optional[str] = Field(default="https://api.cerebras.ai/v1", description="Base URL for Cerebras API.")
    timeout: Optional[float] = Field(default=60.0, description="Request timeout in seconds.")
    max_retries: Optional[int] = Field(default=2, description="Maximum number of retries.")
    
    # Generation parameters
    temperature: Optional[float] = Field(default=None, description="Sampling temperature between 0 and 1.5.")
    top_p: Optional[float] = Field(default=None, description="Nucleus sampling parameter.")
    max_completion_tokens: Optional[int] = Field(default=None, description="Maximum number of tokens to generate.")
    stop_sequences: Optional[List[str]] = Field(default=None, description="Stop sequences for generation.")
    seed: Optional[int] = Field(default=None, description="Random seed for deterministic generation.")
    
    # Model capabilities override
    model_info_override: Optional[ModelInfo] = Field(default=None, description="Override model capabilities.")


__all__ = ["CebrasClientConfiguration"]
