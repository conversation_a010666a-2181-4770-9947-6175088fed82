apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: network-policy
spec:
  description: block some internal communication between services
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues communicating with other services.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl apply --context ${CLUSTER_CTX} -f - <<EOF
        apiVersion: networking.k8s.io/v1
        kind: NetworkPolicy
        metadata:
          name: deny-all
          namespace: default
        spec:
          podSelector: {}
          policyTypes:
          - Ingress
        ---
        apiVersion: networking.k8s.io/v1
        kind: NetworkPolicy
        metadata:
          name: allow-backends
          namespace: default
        spec:
          podSelector:
            matchLabels:
              app: frontend
              version: v1
          policyTypes:
          - Ingress
          ingress:
          - from:
            - podSelector:
                matchLabels:
                  app: backend
                  version: v1
            - podSelector:
                matchLabels:
                  app: backend
                  version: v2
            - podSelector:
                matchLabels:
                  app: backend
                  version: v3
        EOF