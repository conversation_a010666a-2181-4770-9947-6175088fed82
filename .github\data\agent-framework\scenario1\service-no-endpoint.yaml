apiVersion: agent-framework.solo.io/v1alpha1
kind: Challenge
metadata:
  name: service-no-endpoint
spec:
  description: backend-v1 service does not have any endpoint
  prompt: |
    There is a kubernetes cluster running in context 'kind-kagent'.
    Calling the frontend service at http://frontend-v1:8080 I see an error message telling about issues in the 'backend-v1' service.
    To make sure you fixed the environment run the tool `checkKubernetesClusterFixed`. And you have my permission to keep trying out things until you fix the environment and all tests pass.
  steps:
    - run: |
        kubectl --context ${CLUSTER_CTX} delete deployment backend-v1
        kubectl --context ${CLUSTER_CTX} apply -f - <<EOF
        apiVersion: v1
        kind: Service
        metadata:
          name: backend-v1
          namespace: default
          labels:
            app: backend
        spec:
          ports:
          - port: 8080
            protocol: TCP
            targetPort: 8080
          selector:
            app: backend
            version: v1
          type: ClusterIP
        ---
        apiVersion: apps/v1
        kind: Deployment
        metadata:
          name: backend-v1
          namespace: default
          labels:
            app: backend
            version: v1
        spec:
          replicas: 1
          selector:
            matchLabels:
              app: backend
          template:
            metadata:
              labels:
                app: backend
            spec:
              containers:
              - name: backend
                image: nicholasjackson/fake-service:v0.26.2
                ports:
                  - name: http
                    containerPort: 8080
                    protocol: TCP
                livenessProbe:
                  tcpSocket:
                    port: http
                readinessProbe:
                  tcpSocket:
                    port: http
                env:
                - name: "LISTEN_ADDR"
                  value: "0.0.0.0:8080"
                - name: "NAME"
                  value: "backend-v1"
                - name: "MESSAGE"
                  value: "Hello From backend (v1)!"
        EOF