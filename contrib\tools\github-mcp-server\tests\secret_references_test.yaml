suite: test secret references
tests:
  - it: should reference existing secret when tokenSecretRef is provided
    template: templates/toolserver.yaml
    set:
      tools.repositories.readwrite: true
      tools.repositories.tokenSecretRef.name: "existing-secret"
      tools.repositories.tokenSecretRef.key: "github-token"
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.config.streamableHttp.headersFrom[0].valueFrom.valueRef
          value: existing-secret
      - equal:
          path: spec.config.streamableHttp.headersFrom[0].valueFrom.key
          value: github-token

  - it: should not create secret when using tokenSecretRef
    template: templates/secret.yaml
    set:
      tools.repositories.readwrite: true
      tools.repositories.tokenSecretRef.name: "existing-secret"
      tools.repositories.tokenSecretRef.key: "github-token"
    asserts:
      - hasDocuments:
          count: 0

  - it: should prioritize tokenSecretRef over tokenSecret
    template: templates/toolserver.yaml
    set:
      tools.repositories.readwrite: true
      tools.repositories.tokenSecret.value: "ghp_token"
      tools.repositories.tokenSecretRef.name: "existing-secret"
      tools.repositories.tokenSecretRef.key: "github-token"
    asserts:
      - hasDocuments:
          count: 1
      - equal:
          path: spec.config.streamableHttp.headersFrom[0].valueFrom.valueRef
          value: existing-secret
      - equal:
          path: spec.config.streamableHttp.headersFrom[0].valueFrom.key
          value: github-token
