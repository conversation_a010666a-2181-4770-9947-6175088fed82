import React from "react";
import { Sidebar<PERSON>rovider } from "@/components/ui/sidebar";
import { ErrorState } from "@/components/ErrorState";
import { getAgent, getAgents } from "@/app/actions/agents";
import { getTools } from "@/app/actions/tools";
import ChatLayout<PERSON> from "@/components/chat/ChatLayoutUI";

async function getData(agentName: string, namespace: string) {
  try {
    const [teamResponse, teamsResponse, toolsResponse] = await Promise.all([
      getAgent(agentName, namespace),
      getAgents(),
      getTools()
    ]);

    if (teamResponse.error || !teamResponse.data) {
      return { error: teamResponse.error || "Agent not found" };
    }
    if (teamsResponse.error || !teamsResponse.data) {
      return { error: teamsResponse.error || "Failed to fetch agents" };
    }

    const currentAgent = teamResponse.data;
    const allAgents = teamsResponse.data || [];
    const allTools = toolsResponse || [];

    return {
      currentAgent,
      allAgents,
      allTools,
      error: null
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "An unexpected server error occurred";
    console.error("Error fetching data for chat layout:", errorMessage);
    return { error: errorMessage };
  }
}

export default async function ChatLayout({ children, params }: { children: React.ReactNode, params: { name: string, namespace: string } }) {
  const resolvedParams = await params;
  const { name, namespace } = resolvedParams;
  const { currentAgent, allAgents, allTools, error } = await getData(name, namespace);

  if (error || !currentAgent) {
    return (
      <main className="w-full max-w-6xl mx-auto px-4 flex items-center justify-center h-screen">
        <ErrorState message={error || "Agent data could not be loaded."} />
      </main>
    );
  }

  return (
    <SidebarProvider style={{
      "--sidebar-width": "350px",
      "--sidebar-width-mobile": "150px",
    } as React.CSSProperties}>
      <ChatLayoutUI
        agentName={name}
        namespace={namespace}
        currentAgent={currentAgent}
        allAgents={allAgents}
        allTools={allTools}
      >
        {children}
      </ChatLayoutUI>
    </SidebarProvider>
  );
} 