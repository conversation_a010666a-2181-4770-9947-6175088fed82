import asyncio
import logging
from typing import Any, Dict, List, Mapping, Optional, Sequence, Union

from autogen_core import Component
from autogen_core.models import (
    ChatCompletionClient,
    CreateResult,
    LLMMessage,
    RequestUsage,
    SystemMessage,
    UserMessage,
    AssistantMessage,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
)
from autogen_core.tools import Tool, ToolSchema
from autogen_core.cancellation import CancellationToken
from pydantic import BaseModel
from typing_extensions import Unpack
from cerebras.cloud.sdk import Cerebras, AsyncCerebras
from cerebras.cloud.sdk.types import ChatCompletion, ChatCompletionMessage

from ._model_info import get_info
from .config import CebrasClientConfiguration
from .types import ModelInfo

logger = logging.getLogger(__name__)


def _create_args_from_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """Extract create arguments from configuration."""
    create_args = {}
    
    # Map configuration keys to Cerebras API parameters
    param_mapping = {
        "temperature": "temperature",
        "top_p": "top_p",
        "max_completion_tokens": "max_completion_tokens",
        "stop_sequences": "stop",
        "seed": "seed",
    }
    
    for config_key, api_key in param_mapping.items():
        if config_key in config and config[config_key] is not None:
            create_args[api_key] = config[config_key]
    
    return create_args


def _cerebras_client_from_config(config: Mapping[str, Any]) -> AsyncCerebras:
    """Create AsyncCerebras client from configuration."""
    client_config = {}
    
    if "api_key" in config and config["api_key"]:
        client_config["api_key"] = config["api_key"]
    
    if "base_url" in config and config["base_url"]:
        client_config["base_url"] = config["base_url"]
    
    if "timeout" in config and config["timeout"]:
        client_config["timeout"] = config["timeout"]
    
    if "max_retries" in config and config["max_retries"]:
        client_config["max_retries"] = config["max_retries"]
    
    return AsyncCerebras(**client_config)


def _convert_message_to_cerebras_format(message: LLMMessage) -> Dict[str, Any]:
    """Convert autogen message to Cerebras API format."""
    if isinstance(message, SystemMessage):
        return {"role": "system", "content": message.content}
    elif isinstance(message, UserMessage):
        return {"role": "user", "content": message.content}
    elif isinstance(message, AssistantMessage):
        result = {"role": "assistant", "content": message.content}
        if message.tool_calls:
            # Convert tool calls to Cerebras format
            tools = []
            for tool_call in message.tool_calls:
                tools.append({
                    "id": tool_call.id,
                    "type": "function",
                    "function": {
                        "name": tool_call.function.name,
                        "arguments": tool_call.function.arguments,
                    }
                })
            result["tool_calls"] = tools
        return result
    elif isinstance(message, FunctionExecutionResultMessage):
        return {
            "role": "tool",
            "content": message.content[0].content if message.content else "",
            "tool_call_id": message.content[0].call_id if message.content else "",
        }
    else:
        # Fallback for other message types
        return {"role": "user", "content": str(message.content)}


def _convert_tools_to_cerebras_format(tools: Sequence[Tool | ToolSchema]) -> List[Dict[str, Any]]:
    """Convert autogen tools to Cerebras API format."""
    cerebras_tools = []
    
    for tool in tools:
        if isinstance(tool, Tool):
            # Convert Tool to Cerebras format
            cerebras_tools.append({
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters,
                }
            })
        elif isinstance(tool, ToolSchema):
            # Convert ToolSchema to Cerebras format
            cerebras_tools.append({
                "type": "function",
                "function": {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.parameters,
                }
            })
    
    return cerebras_tools


class CebrasChatCompletionClient(ChatCompletionClient, Component[CebrasClientConfiguration]):
    """Cerebras AI chat completion client."""
    
    component_type = "model"
    component_config_schema = CebrasClientConfiguration
    component_provider_override = "kagent.models.cerebras.CebrasChatCompletionClient"
    
    def __init__(self, **kwargs: Unpack[CebrasClientConfiguration]):
        resolved_config = CebrasClientConfiguration(**kwargs)
        
        self._model_name = resolved_config.model
        self._raw_config: Dict[str, Any] = resolved_config.model_dump(warnings=False)
        
        # Extract API key from SecretStr if present
        if resolved_config.api_key:
            self._raw_config["api_key"] = resolved_config.api_key.get_secret_value()
        
        # Create the async client
        self._client = _cerebras_client_from_config(self._raw_config)
        
        # Get model info
        if resolved_config.model_info_override:
            self._model_info = resolved_config.model_info_override
        else:
            self._model_info = get_info(self._model_name)
        
        # Create arguments for API calls
        self._create_args = _create_args_from_config(self._raw_config)
    
    @property
    def capabilities(self) -> ModelInfo:
        """Get model capabilities."""
        return self._model_info
    
    @property
    def count_tokens(self) -> int:
        """Token counting is not implemented for Cerebras."""
        raise NotImplementedError("Token counting is not implemented for Cerebras models")
    
    @property
    def remaining_tokens(self) -> int:
        """Remaining tokens calculation is not implemented for Cerebras."""
        raise NotImplementedError("Remaining tokens calculation is not implemented for Cerebras models")
    
    @property
    def total_tokens(self) -> int:
        """Total tokens calculation is not implemented for Cerebras."""
        raise NotImplementedError("Total tokens calculation is not implemented for Cerebras models")
    
    def __getstate__(self) -> Dict[str, Any]:
        """Serialize the client state."""
        state = self.__dict__.copy()
        state["_client"] = None
        return state
    
    def __setstate__(self, state: Dict[str, Any]) -> None:
        """Deserialize the client state."""
        self.__dict__.update(state)
        self._client = _cerebras_client_from_config(state["_raw_config"])
    
    def _to_config(self) -> CebrasClientConfiguration:
        """Convert to configuration object."""
        copied_config = self._raw_config.copy()
        return CebrasClientConfiguration(**copied_config)
