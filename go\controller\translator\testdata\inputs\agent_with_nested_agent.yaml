operation: translateAgent
targetObject: parent-agent
namespace: test
objects:
  - apiVersion: v1
    kind: Secret
    metadata:
      name: openai-secret
      namespace: test
    data:
      api-key: c2stdGVzdC1hcGkta2V5  # base64 encoded "sk-test-api-key"
  - apiVersion: kagent.dev/v1alpha1
    kind: ModelConfig
    metadata:
      name: nested-model
      namespace: test
    spec:
      provider: OpenAI
      model: gpt-4o
      apiKeySecretRef: openai-secret
      apiKeySecretKey: api-key
  - apiVersion: kagent.dev/v1alpha1
    kind: Agent
    metadata:
      name: specialist-agent
      namespace: test
    spec:
      description: A specialist agent for math problems
      systemMessage: You are a math specialist. Focus on solving mathematical problems step by step.
      modelConfig: nested-model
      tools: []
  - apiVersion: kagent.dev/v1alpha1
    kind: Agent
    metadata:
      name: parent-agent
      namespace: test
    spec:
      description: A parent agent that can delegate to specialists
      systemMessage: You are a coordinating agent that can delegate tasks to specialists.
      modelConfig: nested-model
      tools:
        - agent:
            ref: specialist-agent 